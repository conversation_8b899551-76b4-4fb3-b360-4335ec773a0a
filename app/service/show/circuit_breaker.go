package show

import (
	"context"
	"errors"
	"fmt"
	"log"
	"sync"
	"sync/atomic"
	"time"
)

// CircuitBreakerState 断路器状态
type CircuitBreakerState int32

const (
	// StateClosed 关闭状态 - 正常处理请求
	StateClosed CircuitBreakerState = iota
	// StateOpen 开启状态 - 拒绝所有请求
	StateOpen
	// StateHalfOpen 半开状态 - 允许少量请求通过以测试服务是否恢复
	StateHalfOpen
)

func (s CircuitBreakerState) String() string {
	switch s {
	case StateClosed:
		return "CLOSED"
	case StateOpen:
		return "OPEN"
	case StateHalfOpen:
		return "HALF_OPEN"
	default:
		return "UNKNOWN"
	}
}

// CircuitBreakerConfig 断路器配置
type CircuitBreakerConfig struct {
	// 失败阈值 - 在多少次失败后开启断路器
	FailureThreshold int
	// 成功阈值 - 在半开状态下，多少次成功后转为关闭状态
	SuccessThreshold int
	// 超时时间 - 断路器开启后多久尝试恢复
	Timeout time.Duration
	// 最大半开请求数 - 半开状态下允许的最大并发测试请求数
	MaxHalfOpenRequests int
	// 滑动窗口大小 - 统计最近N个请求
	WindowSize int
	// 失败率阈值 - 失败率超过此值时开启断路器 (0-1)
	FailureRateThreshold float64
	// 最小请求数 - 至少有这么多请求才计算失败率
	MinRequestsToCalcRate int
	// 指数退避配置
	UseExponentialBackoff bool
	MaxBackoffTime        time.Duration
}

// DefaultCircuitBreakerConfig 默认配置
func DefaultCircuitBreakerConfig() *CircuitBreakerConfig {
	return &CircuitBreakerConfig{
		FailureThreshold:      50, // 调整失败阈值为50，提高容错性
		SuccessThreshold:      2,
		Timeout:               30 * time.Second,
		MaxHalfOpenRequests:   1,
		WindowSize:            100,
		FailureRateThreshold:  0.5,
		MinRequestsToCalcRate: 10,
		UseExponentialBackoff: false,
		MaxBackoffTime:        5 * time.Minute,
	}
}

// RequestResult 请求结果
type RequestResult struct {
	Success   bool
	Timestamp time.Time
	Error     error
}

// EnhancedCircuitBreaker 增强版断路器
type EnhancedCircuitBreaker struct {
	config *CircuitBreakerConfig

	// 状态管理
	state           int32 // 使用atomic操作
	lastStateChange time.Time

	// 统计信息
	consecutiveFailures  int32
	consecutiveSuccesses int32
	halfOpenRequests     int32

	// 滑动窗口
	requestHistory []RequestResult
	historyIndex   int

	// 指数退避
	backoffCount  int
	nextRetryTime time.Time

	// 同步控制
	mu     sync.RWMutex
	logger *log.Logger

	// 监控指标
	totalRequests   int64
	totalFailures   int64
	totalSuccesses  int64
	stateChanges    int64
	lastFailureTime time.Time
}

// NewEnhancedCircuitBreaker 创建增强版断路器
func NewEnhancedCircuitBreaker(config *CircuitBreakerConfig, logger *log.Logger) *EnhancedCircuitBreaker {
	if config == nil {
		config = DefaultCircuitBreakerConfig()
	}

	if logger == nil {
		logger = log.New(log.Writer(), "[CircuitBreaker] ", log.LstdFlags)
	}

	cb := &EnhancedCircuitBreaker{
		config:          config,
		state:           int32(StateClosed),
		logger:          logger,
		requestHistory:  make([]RequestResult, config.WindowSize),
		lastStateChange: time.Now(),
	}

	return cb
}

// Call 执行请求
func (cb *EnhancedCircuitBreaker) Call(ctx context.Context, fn func() error) error {
	// 检查是否可以执行请求
	if err := cb.canProceed(ctx); err != nil {
		return err
	}

	// 执行请求
	err := fn()

	// 记录结果
	cb.RecordResult(err == nil, err)

	return err
}

// canProceed 检查是否可以继续执行请求
func (cb *EnhancedCircuitBreaker) canProceed(ctx context.Context) error {
	state := cb.GetState()

	switch state {
	case StateClosed:
		// 关闭状态，允许所有请求
		return nil

	case StateOpen:
		// 检查是否应该转为半开状态
		cb.mu.Lock()
		if cb.shouldAttemptReset() {
			cb.transitionTo(StateHalfOpen)
			cb.mu.Unlock()
			return nil
		}
		cb.mu.Unlock()

		remainingTime := cb.getRemainingTimeout()
		return fmt.Errorf("circuit breaker is OPEN, retry after %v", remainingTime)

	case StateHalfOpen:
		// 半开状态，限制并发测试请求数
		current := atomic.LoadInt32(&cb.halfOpenRequests)
		if current >= int32(cb.config.MaxHalfOpenRequests) {
			return fmt.Errorf("circuit breaker is HALF_OPEN, max concurrent test requests reached")
		}

		// 增加半开请求计数
		atomic.AddInt32(&cb.halfOpenRequests, 1)

		// 确保在请求结束后减少计数
		go func() {
			select {
			case <-ctx.Done():
				atomic.AddInt32(&cb.halfOpenRequests, -1)
			}
		}()

		return nil

	default:
		return fmt.Errorf("circuit breaker is in unknown state: %v", state)
	}
}

// RecordResult 记录请求结果（公开方法供外部调用）
func (cb *EnhancedCircuitBreaker) RecordResult(success bool, err error) {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	// 更新统计
	atomic.AddInt64(&cb.totalRequests, 1)
	if success {
		atomic.AddInt64(&cb.totalSuccesses, 1)
	} else {
		atomic.AddInt64(&cb.totalFailures, 1)
		cb.lastFailureTime = time.Now()
	}

	// 记录到滑动窗口
	cb.requestHistory[cb.historyIndex] = RequestResult{
		Success:   success,
		Timestamp: time.Now(),
		Error:     err,
	}
	cb.historyIndex = (cb.historyIndex + 1) % cb.config.WindowSize

	// 根据当前状态处理结果
	state := CircuitBreakerState(atomic.LoadInt32(&cb.state))

	switch state {
	case StateClosed:
		if !success {
			cb.handleClosedFailure()
		} else {
			// 重置连续失败计数
			atomic.StoreInt32(&cb.consecutiveFailures, 0)
		}

	case StateHalfOpen:
		// 减少半开请求计数
		atomic.AddInt32(&cb.halfOpenRequests, -1)

		if success {
			cb.handleHalfOpenSuccess()
		} else {
			cb.handleHalfOpenFailure()
		}

	case StateOpen:
		// 开启状态不应该有请求通过，这是异常情况
		cb.logger.Printf("[WARN] Request recorded in OPEN state, this should not happen")
	}
}

// handleClosedFailure 处理关闭状态下的失败
func (cb *EnhancedCircuitBreaker) handleClosedFailure() {
	failures := atomic.AddInt32(&cb.consecutiveFailures, 1)

	// 检查连续失败次数
	if failures >= int32(cb.config.FailureThreshold) {
		cb.transitionTo(StateOpen)
		return
	}

	// 检查失败率（如果配置了滑动窗口）
	if cb.config.WindowSize > 0 && cb.config.FailureRateThreshold > 0 {
		failureRate := cb.calculateFailureRate()
		requestCount := cb.getRecentRequestCount()

		if requestCount >= cb.config.MinRequestsToCalcRate &&
			failureRate > cb.config.FailureRateThreshold {
			cb.logger.Printf("[INFO] Failure rate %.2f%% exceeds threshold %.2f%%, opening circuit",
				failureRate*100, cb.config.FailureRateThreshold*100)
			cb.transitionTo(StateOpen)
		}
	}
}

// handleHalfOpenSuccess 处理半开状态下的成功
func (cb *EnhancedCircuitBreaker) handleHalfOpenSuccess() {
	successes := atomic.AddInt32(&cb.consecutiveSuccesses, 1)

	if successes >= int32(cb.config.SuccessThreshold) {
		cb.logger.Printf("[INFO] Half-open test succeeded %d times, closing circuit", successes)
		cb.transitionTo(StateClosed)
		cb.backoffCount = 0 // 重置退避计数
	}
}

// handleHalfOpenFailure 处理半开状态下的失败
func (cb *EnhancedCircuitBreaker) handleHalfOpenFailure() {
	cb.logger.Printf("[WARN] Half-open test failed, reopening circuit")

	// 如果启用了指数退避，增加退避时间
	if cb.config.UseExponentialBackoff {
		cb.backoffCount++
		cb.calculateNextRetryTime()
	}

	cb.transitionTo(StateOpen)
}

// transitionTo 状态转换
func (cb *EnhancedCircuitBreaker) transitionTo(newState CircuitBreakerState) {
	oldState := CircuitBreakerState(atomic.LoadInt32(&cb.state))

	if oldState == newState {
		return
	}

	atomic.StoreInt32(&cb.state, int32(newState))
	cb.lastStateChange = time.Now()
	atomic.AddInt64(&cb.stateChanges, 1)

	// 重置计数器
	switch newState {
	case StateClosed:
		atomic.StoreInt32(&cb.consecutiveFailures, 0)
		atomic.StoreInt32(&cb.consecutiveSuccesses, 0)

	case StateOpen:
		atomic.StoreInt32(&cb.consecutiveSuccesses, 0)
		if !cb.config.UseExponentialBackoff {
			cb.nextRetryTime = time.Now().Add(cb.config.Timeout)
		}

	case StateHalfOpen:
		atomic.StoreInt32(&cb.consecutiveFailures, 0)
		atomic.StoreInt32(&cb.consecutiveSuccesses, 0)
		atomic.StoreInt32(&cb.halfOpenRequests, 0)
	}

	cb.logger.Printf("[STATE] Circuit breaker state changed: %s -> %s",
		oldState.String(), newState.String())
}

// shouldAttemptReset 检查是否应该尝试重置（从开启转为半开）
func (cb *EnhancedCircuitBreaker) shouldAttemptReset() bool {
	if cb.config.UseExponentialBackoff {
		return time.Now().After(cb.nextRetryTime)
	}

	return time.Since(cb.lastStateChange) > cb.config.Timeout
}

// calculateNextRetryTime 计算下次重试时间（指数退避）
func (cb *EnhancedCircuitBreaker) calculateNextRetryTime() {
	baseTimeout := cb.config.Timeout
	backoffMultiplier := 1 << uint(cb.backoffCount) // 2^n
	timeout := time.Duration(backoffMultiplier) * baseTimeout

	if timeout > cb.config.MaxBackoffTime {
		timeout = cb.config.MaxBackoffTime
	}

	cb.nextRetryTime = time.Now().Add(timeout)
	cb.logger.Printf("[BACKOFF] Next retry after %v (backoff count: %d)", timeout, cb.backoffCount)
}

// calculateFailureRate 计算失败率
func (cb *EnhancedCircuitBreaker) calculateFailureRate() float64 {
	var failures, total int

	for _, result := range cb.requestHistory {
		if result.Timestamp.IsZero() {
			continue
		}

		// 只统计最近的请求
		if time.Since(result.Timestamp) < 1*time.Minute {
			total++
			if !result.Success {
				failures++
			}
		}
	}

	if total == 0 {
		return 0
	}

	return float64(failures) / float64(total)
}

// getRecentRequestCount 获取最近的请求数
func (cb *EnhancedCircuitBreaker) getRecentRequestCount() int {
	count := 0
	for _, result := range cb.requestHistory {
		if !result.Timestamp.IsZero() && time.Since(result.Timestamp) < 1*time.Minute {
			count++
		}
	}
	return count
}

// getRemainingTimeout 获取剩余超时时间
func (cb *EnhancedCircuitBreaker) getRemainingTimeout() time.Duration {
	cb.mu.RLock()
	defer cb.mu.RUnlock()

	if cb.config.UseExponentialBackoff {
		remaining := time.Until(cb.nextRetryTime)
		if remaining < 0 {
			return 0
		}
		return remaining
	}

	elapsed := time.Since(cb.lastStateChange)
	remaining := cb.config.Timeout - elapsed

	if remaining < 0 {
		return 0
	}

	return remaining
}

// GetState 获取当前状态
func (cb *EnhancedCircuitBreaker) GetState() CircuitBreakerState {
	return CircuitBreakerState(atomic.LoadInt32(&cb.state))
}

// GetStats 获取统计信息
func (cb *EnhancedCircuitBreaker) GetStats() map[string]interface{} {
	cb.mu.RLock()
	defer cb.mu.RUnlock()

	return map[string]interface{}{
		"state":                 cb.GetState().String(),
		"total_requests":        atomic.LoadInt64(&cb.totalRequests),
		"total_failures":        atomic.LoadInt64(&cb.totalFailures),
		"total_successes":       atomic.LoadInt64(&cb.totalSuccesses),
		"failure_rate":          cb.calculateFailureRate(),
		"consecutive_failures":  atomic.LoadInt32(&cb.consecutiveFailures),
		"consecutive_successes": atomic.LoadInt32(&cb.consecutiveSuccesses),
		"state_changes":         atomic.LoadInt64(&cb.stateChanges),
		"last_state_change":     cb.lastStateChange,
		"last_failure_time":     cb.lastFailureTime,
		"backoff_count":         cb.backoffCount,
	}
}

// Reset 重置断路器
func (cb *EnhancedCircuitBreaker) Reset() {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.transitionTo(StateClosed)
	atomic.StoreInt32(&cb.consecutiveFailures, 0)
	atomic.StoreInt32(&cb.consecutiveSuccesses, 0)
	atomic.StoreInt32(&cb.halfOpenRequests, 0)
	cb.backoffCount = 0

	// 清空历史记录
	cb.requestHistory = make([]RequestResult, cb.config.WindowSize)
	cb.historyIndex = 0

	cb.logger.Printf("[INFO] Circuit breaker has been reset")
}

// IsOpen 检查断路器是否开启
func (cb *EnhancedCircuitBreaker) IsOpen() bool {
	return cb.GetState() == StateOpen
}

// IsHalfOpen 检查断路器是否半开
func (cb *EnhancedCircuitBreaker) IsHalfOpen() bool {
	return cb.GetState() == StateHalfOpen
}

// IsClosed 检查断路器是否关闭
func (cb *EnhancedCircuitBreaker) IsClosed() bool {
	return cb.GetState() == StateClosed
}

// ErrorType 错误类型
type ErrorType int

const (
	// ErrorTypeUnknown 未知错误
	ErrorTypeUnknown ErrorType = iota
	// ErrorTypeTimeout 超时错误
	ErrorTypeTimeout
	// ErrorTypeRateLimit API限流
	ErrorTypeRateLimit
	// ErrorTypeServerError 服务器错误 (5xx)
	ErrorTypeServerError
	// ErrorTypeClientError 客户端错误 (4xx)
	ErrorTypeClientError
	// ErrorTypeNetwork 网络错误
	ErrorTypeNetwork
)

// ClassifyError 对错误进行分类
func ClassifyError(err error) ErrorType {
	if err == nil {
		return ErrorTypeUnknown
	}

	errStr := err.Error()

	// 根据错误信息进行分类
	switch {
	case errors.Is(err, context.DeadlineExceeded):
		return ErrorTypeTimeout
	case contains(errStr, "timeout"):
		return ErrorTypeTimeout
	case contains(errStr, "rate limit") || contains(errStr, "429"):
		return ErrorTypeRateLimit
	case contains(errStr, "500") || contains(errStr, "502") || contains(errStr, "503"):
		return ErrorTypeServerError
	case contains(errStr, "400") || contains(errStr, "401") || contains(errStr, "403") || contains(errStr, "404"):
		return ErrorTypeClientError
	case contains(errStr, "connection") || contains(errStr, "network"):
		return ErrorTypeNetwork
	default:
		return ErrorTypeUnknown
	}
}

// contains 检查字符串是否包含子串（不区分大小写）
func contains(s, substr string) bool {
	return len(s) > 0 && len(substr) > 0 &&
		(s == substr || len(s) > len(substr) &&
			(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr))
}

// ShouldTriggerCircuitBreaker 根据错误类型判断是否应该触发断路器
func ShouldTriggerCircuitBreaker(errType ErrorType) bool {
	switch errType {
	case ErrorTypeTimeout, ErrorTypeRateLimit, ErrorTypeServerError, ErrorTypeNetwork:
		return true
	case ErrorTypeClientError:
		return false // 客户端错误不应该触发断路器
	default:
		return true // 默认触发
	}
}
