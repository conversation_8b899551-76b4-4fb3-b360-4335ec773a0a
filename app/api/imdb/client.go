package imdb

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"os"
	"strings"
	"time"
)

// Client IMDB API客户端
type Client struct {
	config     *MatchConfig
	httpClient *http.Client
	debug      bool
	logger     *log.Logger
	transport  *http.Transport // 保存transport引用用于监控
}

// NewClient 创建新的IMDB API客户端
func NewClient(config *MatchConfig) *Client {
	if config == nil {
		config = DefaultConfig()
	}

	// 创建自定义Transport，优化并发和资源管理
	transport := &http.Transport{
		DisableKeepAlives:     false,            // 启用连接重用，提高性能
		DisableCompression:    false,            // 启用压缩，减少流量
		MaxIdleConns:          8,                // 减少全局空闲连接，避免资源耗尽
		MaxIdleConnsPerHost:   2,                // 减少每个主机空闲连接，适应多进程环境
		IdleConnTimeout:       30 * time.Second, // 减少空闲超时，及时释放连接
		ResponseHeaderTimeout: 60 * time.Second, // 保持响应头超时
		ExpectContinueTimeout: 2 * time.Second,  // 100-continue超时
		TLSHandshakeTimeout:   15 * time.Second, // TLS握手超时
		// 优化多进程并发控制
		MaxConnsPerHost:   3,     // 减少每个主机最大连接数，为多进程留空间
		ForceAttemptHTTP2: false, // 禁用HTTP/2避免复杂性
		// 连接超时控制
		Dial: (&net.Dialer{
			Timeout:   20 * time.Second, // 减少连接超时，快速失败
			KeepAlive: 30 * time.Second, // 减少保持连接时间
		}).Dial,
	}

	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout:   time.Duration(config.Timeout) * time.Second,
			Transport: transport,
		},
		transport: transport, // 保存引用用于监控
		debug:     false,
		logger:    log.New(os.Stdout, "[IMDB Client] ", log.LstdFlags|log.Lmicroseconds),
	}
}

// SetDebug 设置调试模式
func (c *Client) SetDebug(debug bool) {
	c.debug = debug
}

// SetLogger 设置自定义logger
func (c *Client) SetLogger(logger *log.Logger) {
	if logger != nil {
		c.logger = logger
	}
}

// Match 调用IMDB匹配API
func (c *Client) Match(ctx context.Context, req *MatchRequest) (*MatchResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}

	// 为请求设置超时
	if _, ok := ctx.Deadline(); !ok {
		// 如果上下文没有超时，设置一个默认超时
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(c.config.Timeout)*time.Second)
		defer cancel()
	}

	// 🔧 关键修复：确保连接清理
	defer func() {
		if c.transport != nil {
			// 定期清理空闲连接，防止泄漏
			go func() {
				time.Sleep(1 * time.Second)
				c.transport.CloseIdleConnections()
			}()
		}
	}()

	// 🎯 新增：记录连接池状态（调试模式）
	if c.debug && c.logger != nil {
		// 获取连接池统计信息
		if transport, ok := c.httpClient.Transport.(*http.Transport); ok {
			c.logger.Printf("[DEBUG] 连接池状态: MaxIdleConns=%d, MaxIdleConnsPerHost=%d",
				transport.MaxIdleConns, transport.MaxIdleConnsPerHost)
		}
	}

	// 构建请求URL
	url := fmt.Sprintf("%s/match", c.config.BaseURL)

	// 序列化请求体
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Debug: 记录请求体
	if c.debug {
		// TODO: 使用logger输出
		// fmt.Printf("[DEBUG] HTTP Request Body: %s\n", string(jsonData))
	}

	// 执行请求（带重试）
	var lastErr error

	for i := 0; i <= c.config.MaxRetries; i++ {
		// ❗ 关键修复：检查context是否已取消
		select {
		case <-ctx.Done():
			return nil, fmt.Errorf("request cancelled: %w", ctx.Err())
		default:
		}

		if i > 0 {
			// 指数退避重试等待，避免立即重试
			backoffTime := time.Duration(1<<uint(i-1)) * time.Second
			if backoffTime > 15*time.Second { // 增加最大等待时间到15秒
				backoffTime = 15 * time.Second
			}

			if c.debug && c.logger != nil {
				c.logger.Printf("[DEBUG] 重试等待 %v (第%d次重试)", backoffTime, i)
			}

			select {
			case <-ctx.Done():
				return nil, fmt.Errorf("request cancelled during retry wait: %w", ctx.Err())
			case <-time.After(backoffTime):
			}
		}

		// 每次重试都重新创建HTTP请求，确保body不被重复消费
		httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
		if err != nil {
			return nil, fmt.Errorf("failed to create request: %w", err)
		}

		// 设置请求头
		httpReq.Header.Set("Content-Type", "application/json")
		httpReq.Header.Set("User-Agent", "VLab-ExternalIDs-Sync/1.0")

		// ❗ 关键修复：为每个请求设置独立的超时控制
		reqCtx, reqCancel := context.WithTimeout(ctx, time.Duration(c.config.Timeout)*time.Second)
		httpReq = httpReq.WithContext(reqCtx)

		resp, err := c.httpClient.Do(httpReq)
		reqCancel() // 立即释放资源

		// 处理网络错误
		if err != nil {
			// 特殊处理各种连接错误
			if errors.Is(err, io.EOF) || errors.Is(err, io.ErrUnexpectedEOF) ||
				strings.Contains(err.Error(), "EOF") ||
				strings.Contains(err.Error(), "Unsolicited response") ||
				strings.Contains(err.Error(), "connection reset") {
				lastErr = fmt.Errorf("connection error: %w", err)
				if c.debug && c.logger != nil {
					c.logger.Printf("[ERROR] Connection error on attempt %d/%d: %v", i+1, c.config.MaxRetries+1, err)
				}
				break // 连接错误不重试
			} else if ctx.Err() != nil {
				lastErr = fmt.Errorf("request cancelled or timed out: %w", ctx.Err())
				break // 超时不重试
			} else {
				lastErr = fmt.Errorf("request failed: %w", err)
				if c.debug && c.logger != nil {
					c.logger.Printf("[ERROR] Request error on attempt %d/%d: %v", i+1, c.config.MaxRetries+1, err)
				}
				continue // 其他错误继续重试
			}
		}

		// 处理HTTP响应
		if resp.StatusCode == http.StatusOK {
			// 成功，直接返回处理结果
			defer resp.Body.Close()
			body, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, fmt.Errorf("failed to read response body: %w", err)
			}

			// 解析响应
			var matchResp MatchResponse
			if err := json.Unmarshal(body, &matchResp); err != nil {
				return nil, fmt.Errorf("failed to unmarshal response: %w", err)
			}

			// 检查业务错误
			if matchResp.Code != 200 {
				return nil, fmt.Errorf("API error: code=%d, msg=%s", matchResp.Code, matchResp.Msg)
			}

			// 检查匹配分数
			if matchResp.Data != nil && matchResp.Data.MatchScore < c.config.MinScore {
				// Match score below threshold
			}

			return &matchResp, nil
		} else {
			// ❗ 关键修复：非200状态码时必须读尽响应体
			func() {
				defer resp.Body.Close()
				body, readErr := io.ReadAll(resp.Body)
				if readErr != nil {
					lastErr = fmt.Errorf("failed to read error response body: %w", readErr)
				} else {
					lastErr = fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
					if c.debug && c.logger != nil {
						c.logger.Printf("[ERROR] HTTP status %d on attempt %d/%d, body: %s",
							resp.StatusCode, i+1, c.config.MaxRetries+1, string(body))
					}
				}
			}()
		}
	}

	if lastErr != nil {
		return nil, lastErr
	}

	// 如果到这里说明所有重试都失败了
	return nil, fmt.Errorf("all %d attempts failed", c.config.MaxRetries+1)
}

// HealthCheck 健康检查
func (c *Client) HealthCheck(ctx context.Context) error {
	// 创建一个简单的测试请求
	req := &MatchRequest{
		Type:     "movie",
		Title:    "Test",
		Year:     2020,
		Overview: "Test movie",
		Language: "en",
		Genres:   []string{"test"},
	}

	// 尝试调用API
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	_, err := c.Match(ctx, req)
	if err != nil {
		// 检查是否是业务错误（API正常但没有匹配结果）
		if err.Error() == "API error: code=200, msg=No suitable match found" {
			return nil // API正常工作
		}
		return fmt.Errorf("health check failed: %w", err)
	}

	return nil
}

// ParseMediaIDs 从匹配响应中提取媒体ID
func ParseMediaIDs(resp *MatchResponse) *MediaIDs {
	if resp == nil || resp.Data == nil {
		return nil
	}

	// 新版本API: 检查 match 字段（包装器结构）
	if resp.Data.Match != nil {
		// match 字段内部包含 movie 或 show
		if resp.Data.Match.Movie != nil && resp.Data.Match.Movie.IDs != nil {
			// TODO: 使用logger输出
			// fmt.Printf("[DEBUG] 匹配成功(match.movie)！！！！！！ title: %s, year: %d, ids: %+v\n",
			// 	resp.Data.Match.Movie.Title, resp.Data.Match.Movie.Year, resp.Data.Match.Movie.IDs)
			return resp.Data.Match.Movie.IDs
		}
		if resp.Data.Match.Show != nil && resp.Data.Match.Show.IDs != nil {
			// TODO: 使用logger输出
			// fmt.Printf("[DEBUG] 匹配成功(match.show)！！！！！！ title: %s, year: %d, ids: %+v\n",
			// 	resp.Data.Match.Show.Title, resp.Data.Match.Show.Year, resp.Data.Match.Show.IDs)
			return resp.Data.Match.Show.IDs
		}
	}

	// 旧版本API兼容: 直接检查 movie 或 show 字段
	if resp.Data.Movie != nil && resp.Data.Movie.IDs != nil {
		// TODO: 使用logger输出
		// fmt.Printf("[DEBUG] 匹配成功(movie)！！！！！！ title: %s, year: %d, ids: %+v\n",
		// 	resp.Data.Movie.Title, resp.Data.Movie.Year, resp.Data.Movie.IDs)
		return resp.Data.Movie.IDs
	}

	if resp.Data.Show != nil && resp.Data.Show.IDs != nil {
		// TODO: 使用logger输出
		// fmt.Printf("[DEBUG] 匹配成功(show)！！！！！！ title: %s, year: %d, ids: %+v\n",
		// 	resp.Data.Show.Title, resp.Data.Show.Year, resp.Data.Show.IDs)
		return resp.Data.Show.IDs
	}

	// TODO: 使用logger输出
	// fmt.Printf("[DEBUG] no valid IDs found in response\n")
	// fmt.Printf("[DEBUG] resp: %+v\n", resp)

	return nil
}

// IsValidMatch 检查是否为有效匹配
func IsValidMatch(resp *MatchResponse, minScore float64) bool {
	if resp == nil || resp.Data == nil {
		return false
	}

	return resp.Data.IsMatch && resp.Data.MatchScore >= minScore
}

// 全局客户端实例
var defaultClient *Client

// InitDefaultClient 初始化默认客户端
func InitDefaultClient(config *MatchConfig) {
	defaultClient = NewClient(config)
}

// GetDefaultClient 获取默认客户端
func GetDefaultClient() *Client {
	if defaultClient == nil {
		// IMDB client not initialized, creating with default config
		defaultClient = NewClient(DefaultConfig())
	}
	return defaultClient
}

// GetConnectionStats 获取连接池统计信息
func (c *Client) GetConnectionStats() map[string]interface{} {
	stats := make(map[string]interface{})

	if c.transport != nil {
		stats["max_idle_conns"] = c.transport.MaxIdleConns
		stats["max_idle_conns_per_host"] = c.transport.MaxIdleConnsPerHost
		stats["max_conns_per_host"] = c.transport.MaxConnsPerHost
		stats["idle_conn_timeout"] = c.transport.IdleConnTimeout.String()
		stats["response_header_timeout"] = c.transport.ResponseHeaderTimeout.String()
	}

	return stats
}

// CleanupConnections 清理空闲连接
func (c *Client) CleanupConnections() {
	if c.transport != nil {
		c.transport.CloseIdleConnections()
		if c.debug && c.logger != nil {
			c.logger.Printf("[DEBUG] 已清理空闲连接")
		}
	}
}
