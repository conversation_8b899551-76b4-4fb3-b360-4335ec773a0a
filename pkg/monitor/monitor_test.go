package monitor

import (
	"os"
	"testing"
	"time"
)

func TestBasicMonitoring(t *testing.T) {
	// 创建临时目录用于测试
	tempDir := t.TempDir()

	// 创建测试配置
	config := DefaultCmdMonitorConfig()
	config.AppName = "test_app"
	config.LogDirectory = tempDir
	config.EnableConsoleOutput = false // 测试时禁用控制台输出
	config.SamplingRate = 1.0          // 全采样以便测试

	// 创建监控器
	monitor, err := NewCmdMonitor(config)
	if err != nil {
		t.Fatalf("Failed to create monitor: %v", err)
	}
	defer monitor.Stop()

	// 测试基本事件记录
	monitor.RecordEvent("test.event", SeverityInfo, map[string]interface{}{
		"test_data": "value",
	})

	// 测试计数器
	monitor.RecordCounter("test.counter", 5)
	monitor.RecordCounter("test.counter", 3)

	// 测试仪表盘指标
	monitor.RecordGauge("test.gauge", 42.5)

	// 测试时间测量
	err = monitor.TimeFunc("test.timing", map[string]string{
		"operation": "test",
	}, func() error {
		time.Sleep(10 * time.Millisecond)
		return nil
	})

	if err != nil {
		t.Errorf("TimeFunc should not return error: %v", err)
	}

	// 测试错误记录
	testErr := &testError{message: "test error"}
	monitor.RecordError("test.error", testErr, map[string]interface{}{
		"error_context": "testing",
	})

	// 获取统计信息
	stats := monitor.GetStats()

	// 验证统计信息
	if stats == nil {
		t.Error("Stats should not be nil")
	}

	if counters, ok := stats["counters"].(map[string]int64); ok {
		if counters["test.counter"] != 8 {
			t.Errorf("Expected counter value 8, got %d", counters["test.counter"])
		}
	} else {
		t.Error("Counters not found in stats")
	}

	if gauges, ok := stats["gauges"].(map[string]float64); ok {
		if gauges["test.gauge"] != 42.5 {
			t.Errorf("Expected gauge value 42.5, got %f", gauges["test.gauge"])
		}
	} else {
		t.Error("Gauges not found in stats")
	}
}

func TestProgressTracker(t *testing.T) {
	config := DefaultCmdMonitorConfig()
	config.EnableConsoleOutput = false
	config.EnableFileOutput = false
	config.LogDirectory = t.TempDir()

	monitor, err := NewCmdMonitor(config)
	if err != nil {
		t.Fatalf("Failed to create monitor: %v", err)
	}
	defer monitor.Stop()

	// 创建进度跟踪器
	tracker := monitor.NewProgressTracker("test.progress", 100, map[string]string{
		"operation": "test",
	})

	// 更新进度
	for i := 1; i <= 100; i += 10 {
		tracker.Update(int64(i))
		time.Sleep(1 * time.Millisecond) // 短暂延迟
	}

	// 完成跟踪
	tracker.Complete()

	// 验证进度跟踪器已从列表中移除
	stats := monitor.GetStats()
	if progress, ok := stats["progress"].(map[string]interface{}); ok {
		if _, exists := progress["test.progress"]; exists {
			t.Error("Progress tracker should be removed after completion")
		}
	}
}

func TestOperationTracker(t *testing.T) {
	config := DefaultCmdMonitorConfig()
	config.EnableConsoleOutput = false
	config.EnableFileOutput = false
	config.LogDirectory = t.TempDir()

	monitor, err := NewCmdMonitor(config)
	if err != nil {
		t.Fatalf("Failed to create monitor: %v", err)
	}
	defer monitor.Stop()

	// 测试成功操作
	successTracker := monitor.NewOperationTracker("test.operation.success", map[string]string{
		"type": "success",
	}, map[string]interface{}{
		"test": true,
	})

	time.Sleep(5 * time.Millisecond)
	successTracker.Success()

	// 测试失败操作
	errorTracker := monitor.NewOperationTracker("test.operation.error", map[string]string{
		"type": "error",
	}, map[string]interface{}{
		"test": true,
	})

	time.Sleep(5 * time.Millisecond)
	errorTracker.Error(&testError{message: "operation failed"})

	// 验证计数器
	stats := monitor.GetStats()
	if counters, ok := stats["counters"].(map[string]int64); ok {
		if counters["test.operation.success.success_count"] == 0 {
			t.Error("Success counter should be incremented")
		}
		if counters["test.operation.error.error_count"] == 0 {
			t.Error("Error counter should be incremented")
		}
	}
}

func TestSamplingStrategies(t *testing.T) {
	// 测试百分比采样
	percentageSampler := NewPercentageSample(0.5) // 50%

	acceptCount := 0
	totalCount := 1000

	for i := 0; i < totalCount; i++ {
		if percentageSampler.ShouldSample(nil) {
			acceptCount++
		}
	}

	// 检查采样率是否接近期望值（允许一定误差）
	expectedRate := 0.5
	actualRate := float64(acceptCount) / float64(totalCount)
	tolerance := 0.1 // 10% 误差容忍度

	if actualRate < expectedRate-tolerance || actualRate > expectedRate+tolerance {
		t.Errorf("Expected sampling rate around %.2f, got %.2f", expectedRate, actualRate)
	}

	// 测试速率限制采样
	rateLimitSampler := NewRateLimitSample(10) // 每秒10个

	acceptedInSecond := 0
	for i := 0; i < 20; i++ {
		if rateLimitSampler.ShouldSample(nil) {
			acceptedInSecond++
		}
	}

	if acceptedInSecond > 10 {
		t.Errorf("Rate limit sampler should not accept more than 10 samples per second, got %d", acceptedInSecond)
	}

	// 测试严重级别采样
	severitySampler := NewSeveritySample(SeverityWarn)

	testData := []*SampleData{
		{Severity: SeverityDebug},
		{Severity: SeverityInfo},
		{Severity: SeverityWarn},
		{Severity: SeverityError},
		{Severity: SeverityCritical},
	}

	acceptedSeverities := 0
	for _, data := range testData {
		if severitySampler.ShouldSample(data) {
			acceptedSeverities++
		}
	}

	// 应该接受 WARN、ERROR、CRITICAL (3个)
	if acceptedSeverities != 3 {
		t.Errorf("Severity sampler should accept 3 samples, got %d", acceptedSeverities)
	}
}

func TestBufferSampler(t *testing.T) {
	bufferSampler := NewBufferSampler(5) // 最大5个元素

	// 添加6个样本
	for i := 0; i < 6; i++ {
		data := &SampleData{
			Name:  "test.buffer",
			Value: i,
		}
		bufferSampler.Sample(data)
	}

	// 检查缓冲区大小
	buffer := bufferSampler.GetBuffer()
	if len(buffer) != 5 {
		t.Errorf("Buffer size should be 5, got %d", len(buffer))
	}

	// 检查最老的元素是否被移除
	if buffer[0].Value != 1 {
		t.Errorf("First element should be 1 (oldest removed), got %v", buffer[0].Value)
	}

	// 测试清空缓冲区
	bufferSampler.Clear()
	buffer = bufferSampler.GetBuffer()
	if len(buffer) != 0 {
		t.Errorf("Buffer should be empty after clear, got %d elements", len(buffer))
	}
}

func TestFileSampler(t *testing.T) {
	tempDir := t.TempDir()
	testFile := tempDir + "/test_monitor.jsonl"

	fileSampler, err := NewFileSampler(testFile)
	if err != nil {
		t.Fatalf("Failed to create file sampler: %v", err)
	}
	defer fileSampler.Close()

	// 写入一些测试数据
	for i := 0; i < 3; i++ {
		data := &SampleData{
			Timestamp:  time.Now(),
			MetricType: MetricTypeEvent,
			Name:       "test.event",
			Value:      i,
			Severity:   SeverityInfo,
		}

		err := fileSampler.Sample(data)
		if err != nil {
			t.Errorf("Failed to sample data: %v", err)
		}
	}

	// 刷新文件
	err = fileSampler.Flush()
	if err != nil {
		t.Errorf("Failed to flush file: %v", err)
	}

	// 检查文件是否存在
	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		t.Error("Log file should exist")
	}

	// 检查统计信息
	stats := fileSampler.GetStats()
	if stats.AcceptedSamples != 3 {
		t.Errorf("Expected 3 accepted samples, got %d", stats.AcceptedSamples)
	}
}

// testError 用于测试的错误类型
type testError struct {
	message string
}

func (e *testError) Error() string {
	return e.message
}

// 基准测试
func BenchmarkMonitorRecordEvent(b *testing.B) {
	config := DefaultCmdMonitorConfig()
	config.EnableConsoleOutput = false
	config.EnableFileOutput = false
	config.LogDirectory = b.TempDir()

	monitor, err := NewCmdMonitor(config)
	if err != nil {
		b.Fatalf("Failed to create monitor: %v", err)
	}
	defer monitor.Stop()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		monitor.RecordEvent("benchmark.event", SeverityInfo, map[string]interface{}{
			"iteration": i,
		})
	}
}

func BenchmarkMonitorTimeFunc(b *testing.B) {
	config := DefaultCmdMonitorConfig()
	config.EnableConsoleOutput = false
	config.EnableFileOutput = false
	config.LogDirectory = b.TempDir()

	monitor, err := NewCmdMonitor(config)
	if err != nil {
		b.Fatalf("Failed to create monitor: %v", err)
	}
	defer monitor.Stop()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		monitor.TimeFunc("benchmark.timing", map[string]string{
			"iteration": "test",
		}, func() error {
			// 模拟一些工作
			time.Sleep(1 * time.Microsecond)
			return nil
		})
	}
}
