# 命令行工具监控系统 (Command Line Tool Monitoring)

一个专为 Go 命令行工具设计的综合监控和采样系统，支持多种输出格式、采样策略和性能监控。

## 功能特性

### 🎯 核心功能
- **多种采样策略**: 百分比采样、速率限制、严重级别过滤、条件采样
- **灵活输出**: 控制台、文件、内存缓冲区多种输出方式
- **性能监控**: 自动收集系统指标（内存、GC、Goroutine等）
- **进度跟踪**: 长时间运行任务的进度监控和ETA计算
- **操作跟踪**: 函数执行时间、成功/失败状态追踪
- **错误监控**: 详细的错误记录和堆栈跟踪

### 📊 支持的指标类型
- **Counter**: 累计计数器
- **Gauge**: 瞬时值指标
- **Histogram**: 数值分布统计
- **Event**: 事件记录
- **Timing**: 执行时间测量

### 🎛️ 采样策略
- **AlwaysSample**: 总是采样
- **PercentageSample**: 按百分比采样 (0.0-1.0)
- **RateLimitSample**: 速率限制采样 (每秒最大采样数)
- **SeveritySample**: 基于严重级别的采样
- **ConditionalSample**: 自定义条件采样
- **CombinedSamplingStrategy**: 组合多种策略

## 快速开始

### 1. 基础使用

```go
package main

import (
    "time"
    "vlab/pkg/monitor"
)

func main() {
    // 创建监控配置
    config := monitor.DefaultCmdMonitorConfig()
    config.AppName = "my_cli_tool"
    config.Version = "1.0.0"
    
    // 创建监控器
    monitor, err := monitor.NewCmdMonitor(config)
    if err != nil {
        panic(err)
    }
    defer monitor.Stop()
    
    // 记录事件
    monitor.RecordEvent("app.started", monitor.SeverityInfo, map[string]interface{}{
        "config": "production",
    })
    
    // 记录计数器
    monitor.RecordCounter("items.processed", 1)
    
    // 记录执行时间
    monitor.TimeFunc("process_item", map[string]string{
        "item_type": "data",
    }, func() error {
        time.Sleep(100 * time.Millisecond)
        return nil
    })
}
```

### 2. 进度跟踪

```go
// 创建进度跟踪器
tracker := monitor.NewProgressTracker("batch_process", 1000, map[string]string{
    "operation": "data_sync",
})

for i := 0; i < 1000; i++ {
    // 处理项目
    processItem(i)
    
    // 更新进度
    tracker.Update(int64(i + 1))
}

// 完成跟踪
tracker.Complete()
```

### 3. 操作跟踪

```go
// 创建操作跟踪器
opTracker := monitor.NewOperationTracker("api_call", map[string]string{
    "endpoint": "/api/users",
}, map[string]interface{}{
    "user_id": 123,
})

err := makeAPICall()
if err != nil {
    opTracker.Error(err)
} else {
    opTracker.Success()
}
```

## 配置选项

### CmdMonitorConfig 配置详解

```go
config := &monitor.CmdMonitorConfig{
    // 基础配置
    AppName:     "my_app",           // 应用名称
    Version:     "1.0.0",           // 版本号
    Environment: "production",       // 环境标识
    
    // 采样配置
    SamplingRate:           0.1,     // 10% 采样率
    MaxSamplesPerSecond:    100,     // 每秒最大100个样本
    MinSeverity:            monitor.SeverityInfo, // 最低记录INFO级别
    
    // 输出配置
    EnableConsoleOutput:    true,    // 启用控制台输出
    EnableFileOutput:       true,    // 启用文件输出
    EnableBufferOutput:     false,   // 启用内存缓冲
    ConsoleColored:         true,    // 彩色控制台输出
    
    // 文件配置
    LogDirectory:           "./logs", // 日志目录
    LogFilePrefix:          "monitor", // 文件前缀
    LogFileRotate:          true,     // 启用日志轮转
    
    // 系统监控
    EnableSystemMetrics:    true,     // 启用系统指标
    SystemMetricsInterval:  30 * time.Second, // 30秒收集一次
}
```

## 在现有项目中集成

### 1. 修改主函数

```go
// main.go
func main() {
    // 初始化监控系统
    if err := initMonitoring(); err != nil {
        log.Fatal("Failed to initialize monitoring:", err)
    }
    defer stopMonitoring()
    
    // 原有的主逻辑
    executeMainLogic()
}

func initMonitoring() error {
    config := monitor.DefaultCmdMonitorConfig()
    
    // 根据命令行参数调整配置
    if *fastMode {
        config.SamplingRate = 0.1
        config.MinSeverity = monitor.SeverityWarn
    }
    
    var err error
    globalMonitor, err = monitor.NewCmdMonitor(config)
    return err
}

func stopMonitoring() {
    if globalMonitor != nil {
        globalMonitor.Stop()
    }
}
```

### 2. 监控业务逻辑

```go
func processBatch(items []Item) error {
    // 创建进度跟踪
    tracker := globalMonitor.NewProgressTracker("process_batch", int64(len(items)), map[string]string{
        "batch_size": fmt.Sprintf("%d", len(items)),
    })
    defer tracker.Complete()
    
    successCount := 0
    errorCount := 0
    
    for i, item := range items {
        err := globalMonitor.TimeFunc("process_item", map[string]string{
            "item_type": item.Type,
        }, func() error {
            return processItem(item)
        })
        
        if err != nil {
            globalMonitor.RecordError("process_item.failed", err, map[string]interface{}{
                "item_id": item.ID,
            })
            errorCount++
        } else {
            successCount++
        }
        
        tracker.Update(int64(i + 1))
    }
    
    // 记录最终统计
    globalMonitor.RecordCounter("batch.success_count", int64(successCount))
    globalMonitor.RecordCounter("batch.error_count", int64(errorCount))
    
    return nil
}
```

## 输出格式

### 1. 控制台输出
```
[2024-01-15 10:30:45.123] INFO [counter] items.processed: 150
[2024-01-15 10:30:45.124] INFO [event] batch.completed
[2024-01-15 10:30:45.125] ERROR [event] api.failed - ERROR: connection timeout
```

### 2. JSON日志文件
```json
{
  "timestamp": "2024-01-15T10:30:45.123Z",
  "metric_type": "counter",
  "name": "items.processed",
  "value": 150,
  "severity": "info",
  "process_id": 12345,
  "context": {
    "app_name": "my_cli_tool",
    "version": "1.0.0"
  }
}
```

## 采样策略详解

### 1. 百分比采样
```go
// 采样20%的数据
strategy := monitor.NewPercentageSample(0.2)
monitor.SetStrategy(strategy)
```

### 2. 速率限制采样
```go
// 每秒最多50个样本
strategy := monitor.NewRateLimitSample(50)
monitor.SetStrategy(strategy)
```

### 3. 严重级别过滤
```go
// 只记录WARN及以上级别
strategy := monitor.NewSeveritySample(monitor.SeverityWarn)
monitor.SetStrategy(strategy)
```

### 4. 组合策略
```go
// 组合多种策略：20%采样 + 每秒最多100个 + 只记录INFO及以上
percentageStrategy := monitor.NewPercentageSample(0.2)
rateLimitStrategy := monitor.NewRateLimitSample(100)
severityStrategy := monitor.NewSeveritySample(monitor.SeverityInfo)

combinedStrategy := &monitor.CombinedSamplingStrategy{
    Strategies: []monitor.SamplingStrategy{
        percentageStrategy,
        rateLimitStrategy,
        severityStrategy,
    },
}
monitor.SetStrategy(combinedStrategy)
```

## 性能优化建议

### 1. 大批量数据处理
```go
config := monitor.DefaultCmdMonitorConfig()
config.SamplingRate = 0.1              // 降低采样率
config.MaxSamplesPerSecond = 50         // 限制采样频率
config.MinSeverity = monitor.SeverityWarn // 只记录重要事件
config.SystemMetricsInterval = 60 * time.Second // 延长系统指标收集间隔
```

### 2. 实时监控场景
```go
config := monitor.DefaultCmdMonitorConfig()
config.SamplingRate = 1.0               // 全采样
config.EnableBufferOutput = true        // 启用缓冲防止丢失
config.BufferMaxSize = 5000             // 增大缓冲区
config.ProgressReportInterval = 5 * time.Second // 频繁进度报告
```

### 3. 调试模式
```go
config := monitor.DefaultCmdMonitorConfig()
config.SamplingRate = 1.0               // 全采样
config.MinSeverity = monitor.SeverityDebug // 记录所有级别
config.EnableDetailedTiming = true       // 启用详细计时
config.EnableStackTrace = true          // 启用堆栈跟踪
```

## 最佳实践

### 1. 命名规范
- 使用点分隔的层次结构：`api.user.create.duration`
- 动词在最后：`database.query.failed`
- 使用下划线分隔单词：`batch_process.item_count`

### 2. 标签使用
```go
labels := map[string]string{
    "operation": "sync",
    "source":    "api",
    "target":    "database",
}
```

### 3. 上下文信息
```go
context := map[string]interface{}{
    "user_id":    123,
    "batch_size": 100,
    "retry_count": 3,
}
```

### 4. 错误处理
```go
monitor.RecordError("operation.failed", err, map[string]interface{}{
    "operation": "data_sync",
    "item_id":   item.ID,
    "retry_count": retryCount,
})
```

## 故障排除

### 1. 采样率过低
如果发现重要事件被遗漏，检查采样配置：
```go
// 检查当前采样统计
stats := monitor.GetStats()
samplerStats := stats["sampler_stats"].(monitor.SamplerStats)
acceptRate := float64(samplerStats.AcceptedSamples) / float64(samplerStats.TotalSamples)
```

### 2. 性能影响
监控系统对性能的影响：
```go
// 使用异步记录减少阻塞
go func() {
    monitor.RecordEvent("background.task", monitor.SeverityInfo, context)
}()
```

### 3. 内存使用
控制内存使用：
```go
config.BufferMaxSize = 1000        // 限制缓冲区大小
config.EnableBufferOutput = false  // 必要时禁用缓冲
```

## API 参考

### 核心接口
- `NewCmdMonitor(config *CmdMonitorConfig) (*CmdMonitor, error)`
- `RecordEvent(name string, severity Severity, ctx map[string]interface{})`
- `RecordCounter(name string, value int64)`
- `RecordGauge(name string, value float64)`
- `RecordTiming(name string, duration time.Duration, labels map[string]string)`
- `RecordError(name string, err error, ctx map[string]interface{})`
- `TimeFunc(name string, labels map[string]string, fn func() error) error`

### 进度跟踪
- `NewProgressTracker(name string, total int64, labels map[string]string) *ProgressTracker`
- `Update(current int64)`
- `Complete()`

### 操作跟踪
- `NewOperationTracker(name string, labels map[string]string, ctx map[string]interface{}) *OperationTracker`
- `Success()`
- `Error(err error)`

更多详细信息请参考代码文档和示例。
