# 外部ID同步工具 Makefile

BINARY_NAME=external_ids_sync
CONFIG_FILE?=./config/local.ini

.PHONY: build run clean help test

# 构建可执行文件
build:
	@echo "构建 $(BINARY_NAME)..."
	@go build -o $(BINARY_NAME) main.go
	@echo "✅ 构建完成: $(BINARY_NAME)"

# 运行工具（显示帮助）
run: build
	@./$(BINARY_NAME) --help

# 同步单个剧集示例
sync-one: build
	@echo "同步单个剧集示例..."
	@./$(BINARY_NAME) --cmd sync-one --show-id 1 --config $(CONFIG_FILE) --verbose

# 批量同步示例
sync-batch: build
	@echo "批量同步示例..."
	@./$(BINARY_NAME) --cmd sync-batch --show-ids "1,2,3" --config $(CONFIG_FILE)

# 查询示例
query: build
	@echo "查询外部ID示例..."
	@./$(BINARY_NAME) --cmd query --limit 10 --config $(CONFIG_FILE)

# 统计信息
stats: build
	@echo "显示统计信息..."
	@./$(BINARY_NAME) --cmd stats --config $(CONFIG_FILE)

# 测试搜索功能
search-test: build
	@echo "测试搜索功能..."
	@./$(BINARY_NAME) --cmd search --imdb-id tt0903747 --config $(CONFIG_FILE)

# 清理构建产物
clean:
	@echo "清理构建产物..."
	@rm -f $(BINARY_NAME)
	@echo "✅ 清理完成"

# 运行测试
test:
	@echo "运行单元测试..."
	@go test -v ./...

# 检查代码
lint:
	@echo "检查代码..."
	@golangci-lint run ./...

# 格式化代码
fmt:
	@echo "格式化代码..."
	@go fmt ./...

# 显示帮助
help:
	@echo "外部ID同步工具 Makefile 命令："
	@echo ""
	@echo "  make build       - 构建可执行文件"
	@echo "  make run         - 运行工具（显示帮助）"
	@echo "  make sync-one    - 同步单个剧集示例"
	@echo "  make sync-batch  - 批量同步示例"
	@echo "  make query       - 查询外部ID示例"
	@echo "  make stats       - 显示统计信息"
	@echo "  make search-test - 测试搜索功能"
	@echo "  make clean       - 清理构建产物"
	@echo "  make test        - 运行单元测试"
	@echo "  make lint        - 检查代码"
	@echo "  make fmt         - 格式化代码"
	@echo "  make help        - 显示此帮助信息"
	@echo ""
	@echo "环境变量："
	@echo "  CONFIG_FILE - 配置文件路径（默认: ./config/local.ini）"
	@echo ""
	@echo "示例："
	@echo "  CONFIG_FILE=./config/test.ini make stats"

# 默认目标
.DEFAULT_GOAL := help