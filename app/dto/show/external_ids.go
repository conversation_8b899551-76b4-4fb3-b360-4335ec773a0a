package show

import (
	"time"
)

// ExternalIDsRequest 外部ID请求基础结构
type ExternalIDsRequest struct {
	ShowID uint64 `json:"show_id" binding:"required,min=1"` // 剧集ID
}

// SyncExternalIDsRequest 同步外部ID请求
type SyncExternalIDsRequest struct {
	ShowID uint64 `json:"show_id" uri:"id" binding:"required,min=1"` // 剧集ID（从URL路径获取）
}

// BatchSyncExternalIDsRequest 批量同步外部ID请求
type BatchSyncExternalIDsRequest struct {
	ShowIDs []uint64 `json:"show_ids" binding:"required,min=1,dive,min=1"` // 剧集ID列表
}

// UpdateExternalIDsRequest 更新外部ID请求
type UpdateExternalIDsRequest struct {
	ShowID    uint64  `json:"show_id" uri:"id" binding:"required,min=1"`                 // 剧集ID
	ImdbID    *string `json:"imdb_id,omitempty"`                                         // IMDB ID
	TmdbID    *uint32 `json:"tmdb_id,omitempty"`                                         // TMDB ID
	TraktID   *uint32 `json:"trakt_id,omitempty"`                                        // Trakt ID
	Slug      *string `json:"slug,omitempty"`                                            // Slug标识符
	MatchType *string `json:"match_type,omitempty" binding:"omitempty,oneof=movie show"` // 匹配类型
}

// SearchByExternalIDRequest 根据外部ID搜索请求
type SearchByExternalIDRequest struct {
	ImdbID  string `json:"imdb_id,omitempty" form:"imdb_id"`   // IMDB ID
	TmdbID  uint64 `json:"tmdb_id,omitempty" form:"tmdb_id"`   // TMDB ID
	TraktID uint64 `json:"trakt_id,omitempty" form:"trakt_id"` // Trakt ID
	Slug    string `json:"slug,omitempty" form:"slug"`         // Slug
}

// ExternalIDsListRequest 外部ID列表请求
type ExternalIDsListRequest struct {
	ShowIDs       []uint64 `json:"show_ids,omitempty" form:"show_ids"`                 // 剧集ID列表
	MatchType     string   `json:"match_type,omitempty" form:"match_type"`             // 匹配类型
	IsMatch       *uint8   `json:"is_match,omitempty" form:"is_match"`                 // 是否有效匹配
	MinMatchScore *float64 `json:"min_match_score,omitempty" form:"min_match_score"`   // 最小匹配分数
	Page          int      `json:"page" form:"page" binding:"min=1"`                   // 页码
	PageSize      int      `json:"page_size" form:"page_size" binding:"min=1,max=100"` // 每页数量
}

// ExternalIDsResponse 外部ID响应
type ExternalIDsResponse struct {
	ID          uint64                 `json:"id"`                     // 记录ID
	ShowID      uint64                 `json:"show_id"`                // 剧集ID
	TraktID     *uint64                `json:"trakt_id,omitempty"`     // Trakt ID
	Slug        string                 `json:"slug,omitempty"`         // Slug标识符
	ImdbID      string                 `json:"imdb_id,omitempty"`      // IMDB ID
	TmdbID      *uint64                `json:"tmdb_id,omitempty"`      // TMDB ID
	MatchType   string                 `json:"match_type,omitempty"`   // 匹配类型
	MatchScore  *float64               `json:"match_score,omitempty"`  // 匹配分数
	MatchReason string                 `json:"match_reason,omitempty"` // 匹配理由
	IsMatch     uint8                  `json:"is_match"`               // 是否有效匹配
	Source      string                 `json:"source"`                 // 数据来源
	RawResponse map[string]interface{} `json:"raw_response,omitempty"` // 原始响应
	CreatedAt   *time.Time             `json:"created_at"`             // 创建时间
	UpdatedAt   *time.Time             `json:"updated_at"`             // 更新时间
}

// ExternalIDsListResponse 外部ID列表响应
type ExternalIDsListResponse struct {
	List       []*ExternalIDsResponse `json:"list"`        // 数据列表
	Total      int64                  `json:"total"`       // 总数
	Page       int                    `json:"page"`        // 当前页
	PageSize   int                    `json:"page_size"`   // 每页数量
	TotalPages int                    `json:"total_pages"` // 总页数
}

// SyncExternalIDsResponse 同步外部ID响应
type SyncExternalIDsResponse struct {
	ShowID  uint64               `json:"show_id"`        // 剧集ID
	Success bool                 `json:"success"`        // 是否成功
	Message string               `json:"message"`        // 消息
	Data    *ExternalIDsResponse `json:"data,omitempty"` // 同步后的数据
}

// BatchSyncExternalIDsResponse 批量同步外部ID响应
type BatchSyncExternalIDsResponse struct {
	Results      []*SyncResult `json:"results"`       // 同步结果列表
	TotalCount   int           `json:"total_count"`   // 总数
	SuccessCount int           `json:"success_count"` // 成功数
	FailedCount  int           `json:"failed_count"`  // 失败数
}

// SyncResult 同步结果
type SyncResult struct {
	ShowID  uint64               `json:"show_id"`         // 剧集ID
	Success bool                 `json:"success"`         // 是否成功
	Error   string               `json:"error,omitempty"` // 错误信息
	Data    *ExternalIDsResponse `json:"data,omitempty"`  // 同步后的数据
}

// SearchByExternalIDResponse 根据外部ID搜索响应
type SearchByExternalIDResponse struct {
	ShowID      uint64               `json:"show_id,omitempty"`      // 剧集ID
	ExternalIDs *ExternalIDsResponse `json:"external_ids,omitempty"` // 外部ID信息
	Found       bool                 `json:"found"`                  // 是否找到
}

// ExternalIDsStatisticsRequest 统计请求
type ExternalIDsStatisticsRequest struct {
	StartDate *time.Time `json:"start_date,omitempty" form:"start_date"` // 开始日期
	EndDate   *time.Time `json:"end_date,omitempty" form:"end_date"`     // 结束日期
}

// ExternalIDsStatisticsResponse 统计响应
type ExternalIDsStatisticsResponse struct {
	Total      int64            `json:"total"`       // 总记录数
	Matched    int64            `json:"matched"`     // 已匹配数
	NotMatched int64            `json:"not_matched"` // 未匹配数
	MatchRate  float64          `json:"match_rate"`  // 匹配率
	ByType     map[string]int64 `json:"by_type"`     // 按类型统计
	BySource   map[string]int64 `json:"by_source"`   // 按来源统计
	UpdatedAt  *time.Time       `json:"updated_at"`  // 统计时间
}

// ManualMatchRequest 手动匹配请求
type ManualMatchRequest struct {
	ShowID      uint64   `json:"show_id" binding:"required,min=1"`                                 // 剧集ID
	Title       string   `json:"title,omitempty"`                                                  // 标题（可选，不传则使用剧集名称）
	Year        int      `json:"year,omitempty"`                                                   // 年份（可选）
	Overview    string   `json:"overview,omitempty"`                                               // 概述（可选）
	Language    string   `json:"language,omitempty"`                                               // 语言（可选）
	Genres      []string `json:"genres,omitempty"`                                                 // 类型（可选）
	ContentType string   `json:"content_type,omitempty" binding:"omitempty,oneof=movie show auto"` // 内容类型
}

// ManualMatchResponse 手动匹配响应
type ManualMatchResponse struct {
	ShowID      uint64               `json:"show_id"`                // 剧集ID
	Success     bool                 `json:"success"`                // 是否成功
	MatchFound  bool                 `json:"match_found"`            // 是否找到匹配
	MatchScore  float64              `json:"match_score,omitempty"`  // 匹配分数
	ExternalIDs *ExternalIDsResponse `json:"external_ids,omitempty"` // 外部ID信息
	Message     string               `json:"message,omitempty"`      // 消息
}
