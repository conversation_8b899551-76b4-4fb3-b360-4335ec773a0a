package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"runtime"
	"sync"
	"time"

	"vlab/app/common/dbs"
	showService "vlab/app/service/show"
)

// ResourceMonitor 资源监控器
type ResourceMonitor struct {
	logger          *log.Logger
	service         *showService.ExternalIDsService
	stopChan        chan struct{}
	wg              sync.WaitGroup
	checkInterval   time.Duration
	alertThresholds *AlertThresholds
	mu              sync.RWMutex
	lastStats       *ResourceStats
}

// AlertThresholds 告警阈值
type AlertThresholds struct {
	MaxGoroutines       int     // 最大goroutine数量
	MaxMemoryMB         float64 // 最大内存使用(MB)
	MaxDBConnections    int     // 最大数据库连接数
	MaxOpenFiles        int     // 最大打开文件数
	MemoryGrowthRateMB  float64 // 内存增长率(MB/分钟)
}

// ResourceStats 资源统计
type ResourceStats struct {
	Timestamp       time.Time
	Goroutines      int
	MemoryMB        float64
	DBConnections   int
	OpenFiles       int
	HTTPConnStats   map[string]interface{}
	SystemLoad      float64
}

// NewResourceMonitor 创建资源监控器
func NewResourceMonitor(service *showService.ExternalIDsService) *ResourceMonitor {
	return &ResourceMonitor{
		logger:        log.New(os.Stdout, "[ResourceMonitor] ", log.LstdFlags|log.Lmicroseconds),
		service:       service,
		stopChan:      make(chan struct{}),
		checkInterval: 30 * time.Second, // 每30秒检查一次
		alertThresholds: &AlertThresholds{
			MaxGoroutines:      1000,  // 最大1000个goroutine
			MaxMemoryMB:        512,   // 最大512MB内存
			MaxDBConnections:   80,    // 最大80个数据库连接（留20个余量）
			MaxOpenFiles:       1000,  // 最大1000个打开文件
			MemoryGrowthRateMB: 50,    // 每分钟内存增长超过50MB告警
		},
	}
}

// Start 启动监控
func (rm *ResourceMonitor) Start() {
	rm.wg.Add(1)
	go rm.monitorLoop()
	rm.logger.Printf("资源监控器已启动，检查间隔: %v", rm.checkInterval)
}

// Stop 停止监控
func (rm *ResourceMonitor) Stop() {
	close(rm.stopChan)
	rm.wg.Wait()
	rm.logger.Printf("资源监控器已停止")
}

// monitorLoop 监控循环
func (rm *ResourceMonitor) monitorLoop() {
	defer rm.wg.Done()
	
	ticker := time.NewTicker(rm.checkInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-rm.stopChan:
			return
		case <-ticker.C:
			rm.checkResources()
		}
	}
}

// checkResources 检查资源使用情况
func (rm *ResourceMonitor) checkResources() {
	stats := rm.collectStats()
	
	rm.mu.Lock()
	lastStats := rm.lastStats
	rm.lastStats = stats
	rm.mu.Unlock()
	
	// 检查告警条件
	rm.checkAlerts(stats, lastStats)
	
	// 定期输出统计信息
	if stats.Timestamp.Unix()%300 == 0 { // 每5分钟输出一次详细信息
		rm.logDetailedStats(stats)
	}
}

// collectStats 收集资源统计信息
func (rm *ResourceMonitor) collectStats() *ResourceStats {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	stats := &ResourceStats{
		Timestamp:  time.Now(),
		Goroutines: runtime.NumGoroutine(),
		MemoryMB:   float64(m.Alloc) / 1024 / 1024,
	}
	
	// 获取数据库连接统计
	if db := dbs.GetDB(); db != nil {
		if sqlDB, err := db.DB(); err == nil {
			dbStats := sqlDB.Stats()
			stats.DBConnections = dbStats.OpenConnections
		}
	}
	
	// 获取HTTP连接统计（如果可用）
	// 这里可以扩展获取HTTP客户端的连接池信息
	
	return stats
}

// checkAlerts 检查告警条件
func (rm *ResourceMonitor) checkAlerts(current *ResourceStats, last *ResourceStats) {
	// 检查goroutine数量
	if current.Goroutines > rm.alertThresholds.MaxGoroutines {
		rm.logger.Printf("⚠️  ALERT: Goroutine数量过多: %d (阈值: %d)", 
			current.Goroutines, rm.alertThresholds.MaxGoroutines)
		rm.suggestActions("goroutine")
	}
	
	// 检查内存使用
	if current.MemoryMB > rm.alertThresholds.MaxMemoryMB {
		rm.logger.Printf("⚠️  ALERT: 内存使用过多: %.2f MB (阈值: %.2f MB)", 
			current.MemoryMB, rm.alertThresholds.MaxMemoryMB)
		rm.suggestActions("memory")
	}
	
	// 检查数据库连接
	if current.DBConnections > rm.alertThresholds.MaxDBConnections {
		rm.logger.Printf("⚠️  ALERT: 数据库连接过多: %d (阈值: %d)", 
			current.DBConnections, rm.alertThresholds.MaxDBConnections)
		rm.suggestActions("database")
	}
	
	// 检查内存增长率
	if last != nil {
		timeDiff := current.Timestamp.Sub(last.Timestamp).Minutes()
		if timeDiff > 0 {
			memoryGrowth := (current.MemoryMB - last.MemoryMB) / timeDiff
			if memoryGrowth > rm.alertThresholds.MemoryGrowthRateMB {
				rm.logger.Printf("⚠️  ALERT: 内存增长过快: %.2f MB/分钟 (阈值: %.2f MB/分钟)", 
					memoryGrowth, rm.alertThresholds.MemoryGrowthRateMB)
				rm.suggestActions("memory_leak")
			}
		}
	}
}

// suggestActions 建议处理措施
func (rm *ResourceMonitor) suggestActions(alertType string) {
	switch alertType {
	case "goroutine":
		rm.logger.Printf("💡 建议: 检查是否有goroutine泄漏，考虑重启进程")
	case "memory":
		rm.logger.Printf("💡 建议: 触发GC或重启进程")
		runtime.GC() // 主动触发GC
	case "database":
		rm.logger.Printf("💡 建议: 检查数据库连接泄漏，清理长时间运行的查询")
	case "memory_leak":
		rm.logger.Printf("💡 建议: 可能存在内存泄漏，建议重启进程")
	}
}

// logDetailedStats 输出详细统计信息
func (rm *ResourceMonitor) logDetailedStats(stats *ResourceStats) {
	rm.logger.Printf("📊 资源使用统计:")
	rm.logger.Printf("  Goroutines: %d", stats.Goroutines)
	rm.logger.Printf("  内存使用: %.2f MB", stats.MemoryMB)
	rm.logger.Printf("  数据库连接: %d", stats.DBConnections)
	
	// 输出系统信息
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	rm.logger.Printf("  系统内存: %.2f MB", float64(m.Sys)/1024/1024)
	rm.logger.Printf("  GC次数: %d", m.NumGC)
	rm.logger.Printf("  上次GC时间: %v", time.Unix(0, int64(m.LastGC)))
}

// GetCurrentStats 获取当前资源统计
func (rm *ResourceMonitor) GetCurrentStats() *ResourceStats {
	rm.mu.RLock()
	defer rm.mu.RUnlock()
	return rm.lastStats
}

// ForceCleanup 强制清理资源
func (rm *ResourceMonitor) ForceCleanup() {
	rm.logger.Printf("🧹 开始强制清理资源...")
	
	// 触发GC
	runtime.GC()
	runtime.GC() // 连续两次确保清理
	
	// 清理HTTP连接（如果可用）
	// 这里可以调用HTTP客户端的清理方法
	
	rm.logger.Printf("✅ 资源清理完成")
}

// CheckResourceLimits 检查是否接近资源限制
func (rm *ResourceMonitor) CheckResourceLimits() bool {
	stats := rm.collectStats()
	
	// 检查是否接近任何限制（80%阈值）
	if float64(stats.Goroutines) > float64(rm.alertThresholds.MaxGoroutines)*0.8 ||
		stats.MemoryMB > rm.alertThresholds.MaxMemoryMB*0.8 ||
		float64(stats.DBConnections) > float64(rm.alertThresholds.MaxDBConnections)*0.8 {
		
		rm.logger.Printf("⚠️  接近资源限制，建议暂停新任务")
		return true
	}
	
	return false
}

// SetAlertThresholds 设置告警阈值
func (rm *ResourceMonitor) SetAlertThresholds(thresholds *AlertThresholds) {
	rm.mu.Lock()
	defer rm.mu.Unlock()
	rm.alertThresholds = thresholds
}

// 全局资源监控器实例
var globalResourceMonitor *ResourceMonitor

// InitResourceMonitor 初始化全局资源监控器
func InitResourceMonitor(service *showService.ExternalIDsService) {
	globalResourceMonitor = NewResourceMonitor(service)
}

// GetResourceMonitor 获取全局资源监控器
func GetResourceMonitor() *ResourceMonitor {
	return globalResourceMonitor
}
