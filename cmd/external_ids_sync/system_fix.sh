#!/bin/bash

# 系统级超时问题修复脚本
# 专门解决多进程并发导致的资源耗尽问题

echo "=========================================="
echo "🔧 系统级超时问题修复工具"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查是否为root用户
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        echo -e "${YELLOW}⚠️  检测到root权限，将执行系统级修复${NC}"
        return 0
    else
        echo -e "${BLUE}ℹ️  非root用户，将执行用户级修复${NC}"
        return 1
    fi
}

# 检查并修复系统资源限制
fix_system_limits() {
    echo -e "${BLUE}1. 检查系统资源限制...${NC}"
    
    # 检查文件描述符限制
    current_fd_limit=$(ulimit -n)
    echo "当前文件描述符限制: $current_fd_limit"
    
    if [ "$current_fd_limit" -lt 4096 ]; then
        echo -e "${YELLOW}⚠️  文件描述符限制过低，尝试增加...${NC}"
        ulimit -n 4096
        new_limit=$(ulimit -n)
        if [ "$new_limit" -gt "$current_fd_limit" ]; then
            echo -e "${GREEN}✅ 文件描述符限制已增加到: $new_limit${NC}"
        else
            echo -e "${RED}❌ 无法增加文件描述符限制${NC}"
        fi
    else
        echo -e "${GREEN}✅ 文件描述符限制充足${NC}"
    fi
    
    # 检查进程数限制
    current_proc_limit=$(ulimit -u)
    echo "当前进程数限制: $current_proc_limit"
    
    if [ "$current_proc_limit" -lt 2048 ]; then
        echo -e "${YELLOW}⚠️  进程数限制过低${NC}"
    else
        echo -e "${GREEN}✅ 进程数限制充足${NC}"
    fi
}

# 清理网络连接
cleanup_network_connections() {
    echo -e "${BLUE}2. 清理网络连接...${NC}"
    
    # 检查到API服务器的连接数
    api_connections=$(netstat -an 2>/dev/null | grep "*************:5310" | wc -l)
    echo "到API服务器的连接数: $api_connections"
    
    if [ "$api_connections" -gt 10 ]; then
        echo -e "${YELLOW}⚠️  到API服务器的连接数过多${NC}"
        
        # 显示连接状态分布
        echo "连接状态分布:"
        netstat -an 2>/dev/null | grep "*************:5310" | awk '{print $6}' | sort | uniq -c
        
        # 清理TIME_WAIT连接（需要root权限）
        if check_permissions; then
            echo "尝试清理TIME_WAIT连接..."
            # 这里可以添加清理TIME_WAIT连接的命令
        fi
    else
        echo -e "${GREEN}✅ API服务器连接数正常${NC}"
    fi
    
    # 检查总的网络连接数
    total_connections=$(netstat -an 2>/dev/null | grep ESTABLISHED | wc -l)
    echo "总的ESTABLISHED连接数: $total_connections"
    
    if [ "$total_connections" -gt 100 ]; then
        echo -e "${YELLOW}⚠️  网络连接数较多，可能影响性能${NC}"
    else
        echo -e "${GREEN}✅ 网络连接数正常${NC}"
    fi
}

# 检查并清理进程
cleanup_processes() {
    echo -e "${BLUE}3. 检查并清理进程...${NC}"
    
    # 查找external_ids_sync进程
    sync_processes=$(pgrep -f "external_ids_sync" | wc -l)
    echo "external_ids_sync进程数: $sync_processes"
    
    if [ "$sync_processes" -gt 2 ]; then
        echo -e "${YELLOW}⚠️  检测到多个external_ids_sync进程${NC}"
        echo "进程列表:"
        ps aux | grep external_ids_sync | grep -v grep
        
        read -p "是否要清理多余的进程? (y/N): " cleanup_choice
        if [[ $cleanup_choice =~ ^[Yy]$ ]]; then
            echo "清理多余进程..."
            # 保留最新的进程，杀死其他的
            pgrep -f "external_ids_sync" | head -n -1 | xargs -r kill
            echo -e "${GREEN}✅ 已清理多余进程${NC}"
        fi
    else
        echo -e "${GREEN}✅ 进程数正常${NC}"
    fi
}

# 检查数据库连接
check_database_connections() {
    echo -e "${BLUE}4. 检查数据库连接...${NC}"
    
    # 这里需要根据实际数据库配置调整
    if command -v mysql >/dev/null 2>&1; then
        echo "检查MySQL连接数..."
        # 这里可以添加MySQL连接数检查
        echo -e "${BLUE}💡 请手动检查数据库连接数:${NC}"
        echo "   SHOW PROCESSLIST;"
        echo "   SHOW STATUS LIKE 'Threads_connected';"
    else
        echo -e "${YELLOW}⚠️  MySQL客户端未安装，无法检查数据库连接${NC}"
    fi
}

# 优化系统参数
optimize_system_parameters() {
    echo -e "${BLUE}5. 优化系统参数...${NC}"
    
    if check_permissions; then
        echo "优化TCP参数..."
        
        # 减少TIME_WAIT时间
        echo 30 > /proc/sys/net/ipv4/tcp_fin_timeout 2>/dev/null && \
            echo -e "${GREEN}✅ 已减少TCP FIN超时时间${NC}" || \
            echo -e "${RED}❌ 无法修改TCP FIN超时时间${NC}"
        
        # 启用TIME_WAIT重用
        echo 1 > /proc/sys/net/ipv4/tcp_tw_reuse 2>/dev/null && \
            echo -e "${GREEN}✅ 已启用TIME_WAIT重用${NC}" || \
            echo -e "${RED}❌ 无法启用TIME_WAIT重用${NC}"
        
        # 增加本地端口范围
        echo "1024 65535" > /proc/sys/net/ipv4/ip_local_port_range 2>/dev/null && \
            echo -e "${GREEN}✅ 已扩大本地端口范围${NC}" || \
            echo -e "${RED}❌ 无法修改本地端口范围${NC}"
    else
        echo -e "${YELLOW}⚠️  需要root权限才能优化系统参数${NC}"
        echo "建议以root权限运行以下命令:"
        echo "  echo 30 > /proc/sys/net/ipv4/tcp_fin_timeout"
        echo "  echo 1 > /proc/sys/net/ipv4/tcp_tw_reuse"
        echo "  echo '1024 65535' > /proc/sys/net/ipv4/ip_local_port_range"
    fi
}

# 生成修复报告
generate_fix_report() {
    echo -e "${BLUE}6. 生成修复报告...${NC}"
    
    report_file="system_fix_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "系统修复报告 - $(date)"
        echo "=================================="
        echo ""
        echo "系统信息:"
        echo "  操作系统: $(uname -a)"
        echo "  内存使用: $(free -h | grep Mem)"
        echo "  磁盘使用: $(df -h / | tail -1)"
        echo "  负载: $(uptime)"
        echo ""
        echo "网络连接:"
        echo "  总连接数: $(netstat -an 2>/dev/null | grep ESTABLISHED | wc -l)"
        echo "  API连接数: $(netstat -an 2>/dev/null | grep "*************:5310" | wc -l)"
        echo ""
        echo "进程信息:"
        echo "  external_ids_sync进程数: $(pgrep -f "external_ids_sync" | wc -l)"
        echo ""
        echo "资源限制:"
        echo "  文件描述符: $(ulimit -n)"
        echo "  进程数: $(ulimit -u)"
        echo ""
    } > "$report_file"
    
    echo -e "${GREEN}✅ 修复报告已保存到: $report_file${NC}"
}

# 提供修复建议
provide_recommendations() {
    echo -e "${BLUE}7. 修复建议...${NC}"
    
    echo -e "${YELLOW}🔧 针对多进程超时问题的建议:${NC}"
    echo ""
    echo "1. 避免同时运行多个external_ids_sync进程"
    echo "   - 使用进程锁或队列系统"
    echo "   - 分时段运行不同的同步任务"
    echo ""
    echo "2. 优化单个进程的配置"
    echo "   - 减少worker数量: --workers 2"
    echo "   - 增加请求间隔: --delay 2s"
    echo "   - 使用小批次: --page-size 50"
    echo ""
    echo "3. 监控资源使用"
    echo "   - 定期检查连接数: netstat -an | grep *************"
    echo "   - 监控内存使用: free -h"
    echo "   - 检查进程状态: ps aux | grep external_ids_sync"
    echo ""
    echo "4. 系统级优化"
    echo "   - 增加文件描述符限制"
    echo "   - 优化TCP参数"
    echo "   - 配置连接池大小"
    echo ""
    echo -e "${GREEN}💡 推荐的安全运行命令:${NC}"
    echo "   ./external_ids_sync --cmd sync-batch --all --safe --workers 2 --page-size 50"
}

# 主函数
main() {
    echo "开始系统级修复..."
    echo ""
    
    fix_system_limits
    echo ""
    
    cleanup_network_connections
    echo ""
    
    cleanup_processes
    echo ""
    
    check_database_connections
    echo ""
    
    optimize_system_parameters
    echo ""
    
    generate_fix_report
    echo ""
    
    provide_recommendations
    echo ""
    
    echo "=========================================="
    echo -e "${GREEN}🎉 系统修复完成${NC}"
    echo "=========================================="
}

# 检查依赖
check_dependencies() {
    missing_deps=()
    
    if ! command -v netstat >/dev/null 2>&1; then
        missing_deps+=("net-tools")
    fi
    
    if ! command -v pgrep >/dev/null 2>&1; then
        missing_deps+=("procps")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${RED}❌ 缺少依赖: ${missing_deps[*]}${NC}"
        echo "请安装缺少的依赖后重新运行"
        exit 1
    fi
}

# 运行修复
check_dependencies
main
