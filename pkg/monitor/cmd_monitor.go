package monitor

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// CmdMonitorConfig 命令行监控配置
type CmdMonitorConfig struct {
	// 基础配置
	AppName     string `json:"app_name"`
	Version     string `json:"version"`
	Environment string `json:"environment"`

	// 采样配置
	SamplingRate        float64  `json:"sampling_rate"`          // 基础采样率 (0.0-1.0)
	MaxSamplesPerSecond int64    `json:"max_samples_per_second"` // 每秒最大采样数
	MinSeverity         Severity `json:"min_severity"`           // 最小严重级别

	// 输出配置
	EnableConsoleOutput bool `json:"enable_console_output"` // 启用控制台输出
	EnableFileOutput    bool `json:"enable_file_output"`    // 启用文件输出
	EnableBufferOutput  bool `json:"enable_buffer_output"`  // 启用内存缓冲
	ConsoleColored      bool `json:"console_colored"`       // 控制台彩色输出

	// 文件配置
	LogDirectory   string `json:"log_directory"`     // 日志目录
	LogFilePrefix  string `json:"log_file_prefix"`   // 日志文件前缀
	LogFileMaxSize int64  `json:"log_file_max_size"` // 日志文件最大大小(字节)
	LogFileRotate  bool   `json:"log_file_rotate"`   // 是否轮转日志文件

	// 缓冲配置
	BufferMaxSize int `json:"buffer_max_size"` // 缓冲区最大大小

	// 系统监控配置
	EnableSystemMetrics   bool          `json:"enable_system_metrics"`   // 启用系统指标
	SystemMetricsInterval time.Duration `json:"system_metrics_interval"` // 系统指标收集间隔

	// 性能配置
	EnableDetailedTiming bool `json:"enable_detailed_timing"` // 启用详细计时
	EnableStackTrace     bool `json:"enable_stack_trace"`     // 启用堆栈跟踪

	// 进度监控配置
	ProgressReportInterval time.Duration `json:"progress_report_interval"` // 进度报告间隔
	EnableProgressSampling bool          `json:"enable_progress_sampling"` // 启用进度采样
}

// DefaultCmdMonitorConfig 默认配置
func DefaultCmdMonitorConfig() *CmdMonitorConfig {
	return &CmdMonitorConfig{
		AppName:                "cmd_app",
		Version:                "1.0.0",
		Environment:            "development",
		SamplingRate:           1.0,
		MaxSamplesPerSecond:    100,
		MinSeverity:            SeverityInfo,
		EnableConsoleOutput:    true,
		EnableFileOutput:       true,
		EnableBufferOutput:     false,
		ConsoleColored:         true,
		LogDirectory:           "./logs",
		LogFilePrefix:          "monitor",
		LogFileMaxSize:         100 * 1024 * 1024, // 100MB
		LogFileRotate:          true,
		BufferMaxSize:          1000,
		EnableSystemMetrics:    true,
		SystemMetricsInterval:  30 * time.Second,
		EnableDetailedTiming:   true,
		EnableStackTrace:       true,
		ProgressReportInterval: 10 * time.Second,
		EnableProgressSampling: true,
	}
}

// CmdMonitor 命令行监控器
type CmdMonitor struct {
	config        *CmdMonitorConfig
	collector     *MetricsCollector
	sampler       *CompositeSampler
	fileSampler   *FileSampler
	bufferSampler *BufferSampler

	// 进度跟踪
	progressTrackers map[string]*ProgressTracker
	progressMutex    sync.RWMutex

	// 生命周期
	startTime time.Time
	stopped   bool
	stopOnce  sync.Once
}

// ProgressTracker 进度跟踪器
type ProgressTracker struct {
	name       string
	total      int64
	current    int64
	startTime  time.Time
	lastReport time.Time
	labels     map[string]string
	monitor    *CmdMonitor
	mutex      sync.RWMutex
}

// NewCmdMonitor 创建命令行监控器
func NewCmdMonitor(config *CmdMonitorConfig) (*CmdMonitor, error) {
	if config == nil {
		config = DefaultCmdMonitorConfig()
	}

	// 创建组合采样器
	compositeSampler := NewCompositeSampler()

	// 设置采样策略
	var strategy SamplingStrategy
	if config.SamplingRate < 1.0 {
		// 使用百分比采样
		percentageStrategy := NewPercentageSample(config.SamplingRate)
		if config.MaxSamplesPerSecond > 0 {
			// 组合百分比和速率限制
			rateLimitStrategy := NewRateLimitSample(config.MaxSamplesPerSecond)
			strategy = &CombinedSamplingStrategy{
				strategies: []SamplingStrategy{percentageStrategy, rateLimitStrategy},
			}
		} else {
			strategy = percentageStrategy
		}
	} else if config.MaxSamplesPerSecond > 0 {
		strategy = NewRateLimitSample(config.MaxSamplesPerSecond)
	} else {
		strategy = &AlwaysSample{}
	}

	// 添加严重级别过滤
	if config.MinSeverity != SeverityDebug {
		severityStrategy := NewSeveritySample(config.MinSeverity)
		if _, ok := strategy.(*AlwaysSample); ok {
			strategy = severityStrategy
		} else {
			strategy = &CombinedSamplingStrategy{
				strategies: []SamplingStrategy{strategy, severityStrategy},
			}
		}
	}

	compositeSampler.SetStrategy(strategy)

	monitor := &CmdMonitor{
		config:           config,
		sampler:          compositeSampler,
		progressTrackers: make(map[string]*ProgressTracker),
		startTime:        time.Now(),
	}

	// 添加采样器
	if err := monitor.setupSamplers(); err != nil {
		return nil, fmt.Errorf("failed to setup samplers: %w", err)
	}

	// 创建指标收集器
	monitor.collector = NewMetricsCollector(compositeSampler)

	// 启用系统指标收集
	if config.EnableSystemMetrics {
		monitor.collector.EnableSystemMetrics(config.SystemMetricsInterval)
	}

	// 记录启动事件
	monitor.RecordEvent("monitor.started", SeverityInfo, map[string]interface{}{
		"config": config,
		"pid":    os.Getpid(),
	})

	return monitor, nil
}

// setupSamplers 设置采样器
func (cm *CmdMonitor) setupSamplers() error {
	// 控制台采样器
	if cm.config.EnableConsoleOutput {
		consoleSampler := NewConsoleSampler(cm.config.ConsoleColored)
		cm.sampler.AddSampler(consoleSampler)
	}

	// 文件采样器
	if cm.config.EnableFileOutput {
		// 确保日志目录存在
		if err := os.MkdirAll(cm.config.LogDirectory, 0755); err != nil {
			return fmt.Errorf("failed to create log directory: %w", err)
		}

		// 生成日志文件名
		timestamp := time.Now().Format("20060102_150405")
		filename := fmt.Sprintf("%s_%s.jsonl", cm.config.LogFilePrefix, timestamp)
		logPath := filepath.Join(cm.config.LogDirectory, filename)

		fileSampler, err := NewFileSampler(logPath)
		if err != nil {
			return fmt.Errorf("failed to create file sampler: %w", err)
		}

		cm.fileSampler = fileSampler
		cm.sampler.AddSampler(fileSampler)
	}

	// 内存缓冲采样器
	if cm.config.EnableBufferOutput {
		bufferSampler := NewBufferSampler(cm.config.BufferMaxSize)
		cm.bufferSampler = bufferSampler
		cm.sampler.AddSampler(bufferSampler)
	}

	return nil
}

// RecordEvent 记录事件
func (cm *CmdMonitor) RecordEvent(name string, severity Severity, ctx map[string]interface{}) {
	if cm.stopped {
		return
	}
	cm.collector.RecordEvent(name, severity, cm.enrichContext(ctx))
}

// RecordCounter 记录计数器
func (cm *CmdMonitor) RecordCounter(name string, value int64) {
	if cm.stopped {
		return
	}
	cm.collector.RecordCounter(name, value)
}

// RecordGauge 记录仪表盘指标
func (cm *CmdMonitor) RecordGauge(name string, value float64) {
	if cm.stopped {
		return
	}
	cm.collector.RecordGauge(name, value)
}

// RecordTiming 记录时间指标
func (cm *CmdMonitor) RecordTiming(name string, duration time.Duration, labels map[string]string) {
	if cm.stopped {
		return
	}
	cm.collector.RecordTiming(name, duration, labels)
}

// RecordError 记录错误
func (cm *CmdMonitor) RecordError(name string, err error, ctx map[string]interface{}) {
	if cm.stopped {
		return
	}
	cm.collector.RecordError(name, err, cm.enrichContext(ctx))
}

// TimeFunc 测量函数执行时间
func (cm *CmdMonitor) TimeFunc(name string, labels map[string]string, fn func() error) error {
	if cm.stopped {
		return fn() // 如果监控已停止，直接执行函数
	}
	return cm.collector.TimeFunc(name, labels, fn)
}

// NewProgressTracker 创建进度跟踪器
func (cm *CmdMonitor) NewProgressTracker(name string, total int64, labels map[string]string) *ProgressTracker {
	cm.progressMutex.Lock()
	defer cm.progressMutex.Unlock()

	tracker := &ProgressTracker{
		name:       name,
		total:      total,
		current:    0,
		startTime:  time.Now(),
		lastReport: time.Now(),
		labels:     labels,
		monitor:    cm,
	}

	cm.progressTrackers[name] = tracker

	// 记录进度跟踪开始
	cm.RecordEvent("progress.started", SeverityInfo, map[string]interface{}{
		"progress_name": name,
		"total":         total,
		"labels":        labels,
	})

	return tracker
}

// Update 更新进度
func (pt *ProgressTracker) Update(current int64) {
	pt.mutex.Lock()
	defer pt.mutex.Unlock()

	pt.current = current
	now := time.Now()

	// 检查是否需要报告进度
	shouldReport := false
	if pt.monitor.config.EnableProgressSampling {
		if now.Sub(pt.lastReport) >= pt.monitor.config.ProgressReportInterval {
			shouldReport = true
			pt.lastReport = now
		}
	}

	if shouldReport {
		elapsed := now.Sub(pt.startTime)
		percentage := float64(current) / float64(pt.total) * 100

		var eta time.Duration
		if current > 0 {
			rate := float64(current) / elapsed.Seconds()
			remaining := pt.total - current
			eta = time.Duration(float64(remaining)/rate) * time.Second
		}

		pt.monitor.RecordEvent("progress.update", SeverityInfo, map[string]interface{}{
			"progress_name": pt.name,
			"current":       current,
			"total":         pt.total,
			"percentage":    percentage,
			"elapsed":       elapsed.String(),
			"eta":           eta.String(),
			"labels":        pt.labels,
		})

		pt.monitor.RecordGauge(pt.name+".progress_percentage", percentage)
		pt.monitor.RecordGauge(pt.name+".current", float64(current))
	}
}

// Complete 完成进度跟踪
func (pt *ProgressTracker) Complete() {
	pt.mutex.Lock()
	defer pt.mutex.Unlock()

	elapsed := time.Since(pt.startTime)

	pt.monitor.RecordEvent("progress.completed", SeverityInfo, map[string]interface{}{
		"progress_name": pt.name,
		"total":         pt.total,
		"elapsed":       elapsed.String(),
		"labels":        pt.labels,
	})

	pt.monitor.RecordTiming(pt.name+".duration", elapsed, pt.labels)

	// 从跟踪器列表中移除
	pt.monitor.progressMutex.Lock()
	delete(pt.monitor.progressTrackers, pt.name)
	pt.monitor.progressMutex.Unlock()
}

// NewOperationTracker 创建操作跟踪器
func (cm *CmdMonitor) NewOperationTracker(name string, labels map[string]string, ctx map[string]interface{}) *OperationTracker {
	return cm.collector.NewOperationTracker(name, labels, cm.enrichContext(ctx))
}

// enrichContext 丰富上下文信息
func (cm *CmdMonitor) enrichContext(ctx map[string]interface{}) map[string]interface{} {
	if ctx == nil {
		ctx = make(map[string]interface{})
	}

	ctx["app_name"] = cm.config.AppName
	ctx["version"] = cm.config.Version
	ctx["environment"] = cm.config.Environment
	ctx["uptime"] = time.Since(cm.startTime).String()
	ctx["pid"] = os.Getpid()

	return ctx
}

// GetStats 获取监控统计信息
func (cm *CmdMonitor) GetStats() map[string]interface{} {
	stats := cm.collector.GetCurrentStats()

	// 添加进度信息
	cm.progressMutex.RLock()
	progressInfo := make(map[string]interface{})
	for name, tracker := range cm.progressTrackers {
		tracker.mutex.RLock()
		progressInfo[name] = map[string]interface{}{
			"current":    tracker.current,
			"total":      tracker.total,
			"percentage": float64(tracker.current) / float64(tracker.total) * 100,
			"elapsed":    time.Since(tracker.startTime).String(),
			"labels":     tracker.labels,
		}
		tracker.mutex.RUnlock()
	}
	cm.progressMutex.RUnlock()

	stats["progress"] = progressInfo
	stats["monitor_config"] = cm.config
	stats["uptime"] = time.Since(cm.startTime).String()

	return stats
}

// DumpBuffer 导出缓冲区数据
func (cm *CmdMonitor) DumpBuffer(filepath string) error {
	if cm.bufferSampler == nil {
		return fmt.Errorf("buffer sampler not enabled")
	}

	return cm.bufferSampler.DumpToFile(filepath)
}

// Stop 停止监控
func (cm *CmdMonitor) Stop() error {
	var err error
	cm.stopOnce.Do(func() {
		cm.stopped = true

		// 记录停止事件
		cm.RecordEvent("monitor.stopping", SeverityInfo, map[string]interface{}{
			"uptime": time.Since(cm.startTime).String(),
		})

		// 完成所有进度跟踪
		cm.progressMutex.Lock()
		for _, tracker := range cm.progressTrackers {
			tracker.Complete()
		}
		cm.progressMutex.Unlock()

		// 刷新并关闭采样器
		if cm.sampler != nil {
			cm.sampler.Flush()
		}

		// 关闭收集器
		if cm.collector != nil {
			err = cm.collector.Close()
		}

		// 记录最终统计
		stats := cm.GetStats()
		cm.RecordEvent("monitor.stopped", SeverityInfo, map[string]interface{}{
			"final_stats": stats,
		})

		// 最终刷新
		if cm.sampler != nil {
			cm.sampler.Flush()
			cm.sampler.Close()
		}
	})

	return err
}

// CombinedSamplingStrategy 组合采样策略
type CombinedSamplingStrategy struct {
	strategies []SamplingStrategy
}

func (css *CombinedSamplingStrategy) ShouldSample(data interface{}) bool {
	for _, strategy := range css.strategies {
		if !strategy.ShouldSample(data) {
			return false
		}
	}
	return true
}

func (css *CombinedSamplingStrategy) GetName() string {
	names := make([]string, len(css.strategies))
	for i, strategy := range css.strategies {
		names[i] = strategy.GetName()
	}
	return fmt.Sprintf("combined(%v)", names)
}
