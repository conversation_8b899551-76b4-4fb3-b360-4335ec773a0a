# 外部ID同步超时问题修复方案

## 问题分析

根据报错信息分析，主要问题包括：

1. **API调用超时**：`external_api_call_retry timeout after 35s`
2. **数据库查询超时**：`check_existing timeout after 1m0s`
3. **重复超时错误**：同样的错误在多个剧集上重复出现

## 修复内容

### 1. 增加超时时间配置

#### API层面
- **IMDB API默认超时**：从30秒增加到60秒
- **HTTP客户端超时**：从30秒增加到60秒
- **服务层API调用超时**：从35秒增加到90秒
- **主程序API调用超时**：从35秒增加到120秒

#### 数据库层面
- **数据库操作超时**：从5秒增加到15秒
- **检查现有记录超时**：从60秒增加到120秒

### 2. 优化HTTP客户端配置

```go
// 优化后的Transport配置
transport := &http.Transport{
    MaxIdleConns:          15,               // 增加连接池大小
    MaxIdleConnsPerHost:   5,                // 每个主机保持更多连接
    IdleConnTimeout:       60 * time.Second, // 增加空闲超时
    ResponseHeaderTimeout: 60 * time.Second, // 增加响应头超时
    MaxConnsPerHost:       5,                // 增加连接数，减少排队
    Dial: (&net.Dialer{
        Timeout:   30 * time.Second, // 增加连接超时
        KeepAlive: 60 * time.Second, // 增加保持连接活跃时间
    }).Dial,
}
```

### 3. 增强健康检查功能

新增了完整的系统健康检查，包括：
- 数据库连接测试
- 数据库表结构和索引检查
- 数据库查询性能测试
- 网络连接测试
- 外部API服务检查
- 断路器状态检查

### 4. 添加网络诊断工具

创建了 `diagnose.sh` 脚本，用于快速诊断：
- 网络连接状态
- 数据库连接
- 系统资源使用
- API响应时间
- 自动提供解决方案建议

## 使用方法

### 1. 运行健康检查

```bash
# 完整的系统健康检查
./external_ids_sync --cmd health-check

# 使用诊断脚本
./diagnose.sh
```

### 2. 推荐的同步命令

```bash
# 网络不稳定时使用安全模式
./external_ids_sync --cmd sync-batch --all --safe

# API频繁卡住时使用激进模式
./external_ids_sync --cmd sync-batch --all --aggressive

# 重置断路器后同步
./external_ids_sync --cmd sync-batch --all --reset-circuit-breaker

# 单个剧集调试
./external_ids_sync --cmd sync-one --show-id <ID> --debug
```

### 3. 故障排除步骤

1. **首先运行诊断**：
   ```bash
   ./diagnose.sh
   ```

2. **检查系统健康**：
   ```bash
   ./external_ids_sync --cmd health-check
   ```

3. **根据诊断结果选择合适的同步模式**：
   - 网络稳定：正常模式
   - 网络不稳定：`--safe` 模式
   - API频繁卡住：`--aggressive` 模式

4. **如果断路器开启**：
   ```bash
   ./external_ids_sync --cmd sync-batch --all --reset-circuit-breaker
   ```

## 预期效果

修复后的系统应该能够：

1. **更好地处理网络延迟**：增加的超时时间给网络波动留出更多空间
2. **减少超时错误**：合理的超时配置降低误报
3. **提供更好的诊断能力**：快速定位问题根源
4. **自动恢复能力**：断路器机制防止级联失败

## 监控建议

1. **定期运行健康检查**：
   ```bash
   # 每天运行一次
   ./external_ids_sync --cmd health-check
   ```

2. **监控关键指标**：
   - API响应时间
   - 数据库查询时间
   - 断路器状态
   - 成功率

3. **日志分析**：
   - 关注超时错误的频率
   - 监控断路器开启/关闭事件
   - 跟踪性能趋势

## 注意事项

1. **超时时间调整**：根据实际网络环境可能需要进一步调整
2. **资源使用**：增加的超时时间可能导致更多资源占用
3. **并发控制**：在网络不稳定时建议减少并发数
4. **定期维护**：定期检查和清理可能的资源泄漏

## 回滚方案

如果修复后出现问题，可以通过以下方式回滚：

1. **恢复原始超时配置**：
   - API超时：30秒
   - 数据库超时：5秒
   - 检查超时：60秒

2. **使用保守的同步参数**：
   ```bash
   ./external_ids_sync --cmd sync-one --show-id <ID> --debug
   ```

3. **监控系统资源使用**，确保没有资源泄漏
