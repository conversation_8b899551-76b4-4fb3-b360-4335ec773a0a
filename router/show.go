package router

import (
	"vlab/app/handler/show"
	mw "vlab/app/middleware"

	"github.com/gin-gonic/gin"
)

func loadShowApi(e *gin.RouterGroup) {
	e.GET("/class/list", mw.ReplayProtection(), show.ClassList) // class/list 类型列表

	sh := e.Group("/show/", mw.ReplayProtection(), mw.CheckUser())
	{
		sh.GET("/list", show.ShowList) // show/list 首页剧集列表

		sh.GET("/detail", show.ShowDetail) // show/detail 剧集详情

		sh.GET("/search", show.ShowSearch) // show/search 剧集搜索

		sh.GET("/recommend/list", show.ShowRecommendList) // show/recommend/list 推荐剧集列表

		sh.GET("/popular/list", show.ShowPopularList) // show/popular/list 热门剧集列表

		sh.GET("/assign/list", show.ShowAssignList) // show/assign/list 指定剧集列表

	}

	ep := e.Group("/episode/", mw.ReplayProtection(), mw.CheckUser())
	{
		ep.GET("/detail", show.EpisodeDetail)     // episode/detail 集详情
		ep.GET("/download", show.EpisodeDownload) // episode/detail 集下载
	}

	bn := e.Group("/market/banner/", mw.ReplayProtection(), mw.CheckUser())
	{
		bn.GET("/list", show.MarketBannerList) // market/banner/list 轮播列表
	}
}

func loadAdminShowApi(e *gin.RouterGroup) {
	e.POST("/admin/i18n", mw.CheckAccountLogin(), show.AdminI18nCreate) // admin/i18n 创建国际化
	e.GET("/admin/i18n", mw.CheckAccountLogin(), show.AdminI18nDetail)  // admin/i18n 国际化详情

	sh := e.Group("/admin/show/", mw.CheckAccountLogin())
	{
		sh.GET("/list", show.AdminShowList) // admin/show/list 剧集列表

		sh.GET("/detail", show.AdminShowDetail) // admin/show/detail 剧集详情

		sh.POST("/create", show.AdminShowCreate) // admin/show/create 创建剧集

		sh.PUT("/update", show.AdminShowUpdate) // admin/show/update 更新剧集

		sh.PUT("/update/status/batch", show.AdminShowStatusBatch) // admin/show/update/status/batch 批量更新剧集状态

		sh.PATCH("/update", show.AdminShowUpdatePatch) // admin/show/update 部分更新剧集

		sh.DELETE("/delete", show.AdminShowDelete) // admin/show/delete 删除剧集

		sh.GET("/tmdb", show.AdminShowTmdb) // admin/show/tmdb 通过tmdb 名字获取剧集信息

		sh.GET("/tmdb/detail", show.AdminShowTmdbDetail) // admin/show/tmdb/detail 通过tmdb id获取剧集信息

		sh.GET("/tmdb/url", show.AdminShowTmdbUrl) // admin/show/tmdb/url 通过剧名获取剧集url

		sh.POST("/vector/upload", show.AdminShowVectorUpload) // admin/show/vector/upload 单个剧集向量上传

		// 向量同步相关
		sh.POST("/vector/sync/manual", show.VectorSyncManual) // admin/show/vector/sync/manual 手动触发向量同步
		sh.POST("/vector/sync/job", show.VectorSyncJob)       // admin/show/vector/sync/job 测试定时任务
	}

	ep := e.Group("/admin/episode/", mw.CheckAccountLogin())
	{
		ep.GET("/list", show.AdminEpisodeList) // admin/episode/list 集列表

		ep.GET("/detail", show.AdminEpisodeDetail) // admin/episode/detail 集详情

		ep.POST("/create", show.AdminEpisodeCreate) // admin/episode/create 创建集

		ep.POST("/create/batch", show.AdminEpisodeCreateBatch) // admin/episode/create/batch 批量创建集

		ep.PUT("/update", show.AdminEpisodeUpdate) // admin/episode/update 更新集

		ep.PATCH("/update", show.AdminEpisodeUpdatePatch) // admin/episode/update 部分更新集

	}

	su := e.Group("/admin/subtitle/", mw.CheckAccountLogin())
	{
		su.GET("/list", show.AdminSubtitleList) // admin/subtitle/list 字幕列表

		su.GET("/detail", show.AdminSubtitleDetail) // admin/subtitle/detail 字幕详情

		su.POST("/create", show.AdminSubtitleCreate) // admin/subtitle/create 创建字幕

		su.POST("/create/batch", show.AdminSubtitleCreateBatch) // admin/subtitle/create/batch 批量创建字幕

		su.PUT("/update", show.AdminSubtitleUpdate) // admin/subtitle/update 更新字幕

		su.DELETE("/delete", show.AdminSubtitleDelete) // admin/subtitle/delete 删除字幕

	}

	ge := e.Group("/admin/genre/", mw.CheckAccountLogin())
	{
		ge.GET("/list", show.AdminGenreList) // admin/genre/list 类型列表

		ge.GET("/detail", show.AdminGenreDetail) // admin/genre/detail 类型详情

		ge.POST("/create", show.AdminGenreCreate) // admin/genre/create 创建类型

		ge.PUT("/update", show.AdminGenreUpdate) // admin/genre/update 更新类型

		ge.PATCH("/update", show.AdminGenreUpdatePatch) // admin/genre/update 部分更新类型

		ge.DELETE("/delete", show.AdminGenreDelete) // admin/genre/delete 删除类型
	}

	fr := e.Group("/admin/franchise/", mw.CheckAccountLogin())
	{
		fr.GET("/list", show.AdminFranchiseList) // admin/franchise/list 系列列表

		fr.GET("/detail", show.AdminFranchiseDetail) // admin/franchise/detail 系列详情

		fr.POST("/create", show.AdminFranchiseCreate) // admin/franchise/create 创建系列

		fr.PUT("/update", show.AdminFranchiseUpdate) // admin/franchise/update 更新系列

		fr.PATCH("/update", show.AdminFranchiseUpdatePatch) // admin/franchise/update 部分更新系列

		fr.DELETE("/delete", show.AdminFranchiseDelete) // admin/franchise/delete 删除系列
	}

	pe := e.Group("/admin/person/", mw.CheckAccountLogin())
	{
		pe.GET("/list", show.AdminPersonList) // admin/person/list 人物列表

		pe.GET("/detail", show.AdminPersonDetail) // admin/person/detail 人物详情

		pe.POST("/create", show.AdminPersonCreate) // admin/person/create 创建人物

		pe.PUT("/update", show.AdminPersonUpdate) // admin/person/update 更新人物

		pe.PATCH("/update", show.AdminPersonUpdatePatch) // admin/person/update 部分更新人物

		pe.DELETE("/delete", show.AdminPersonDelete) // admin/person/delete 删除人物
	}

	// 热度
	ho := e.Group("/admin/popular/", mw.CheckAccountLogin())
	{
		ho.GET("/list", show.AdminPopularList) // admin/popular/list 热度列表

		ho.GET("/detail", show.AdminPopularDetail) // admin/popular/detail 热度详情

		ho.POST("/create", show.AdminPopularCreate) // admin/popular/create 创建热度

		ho.PUT("/update", show.AdminPopularUpdate) // admin/popular/update 更新热度

		ho.PATCH("/update", show.AdminPopularUpdatePatch) // admin/popular/update 部分更新热度

		ho.DELETE("/delete", show.AdminPopularDelete) // admin/popular/delete 删除热度
	}

	// 指定
	ag := e.Group("/admin/assign/", mw.CheckAccountLogin())
	{
		ag.GET("/list", show.AdminAssignList) // admin/assign/list 指定列表

		ag.GET("/detail", show.AdminAssignDetail) // admin/assign/detail 指定详情

		ag.POST("/create", show.AdminAssignCreate) // admin/assign/create 创建指定

		ag.PUT("/update", show.AdminAssignUpdate) // admin/assign/update 更新指定

		ag.PATCH("/update", show.AdminAssignUpdatePatch) // admin/assign/update 部分更新指定

		ag.DELETE("/delete", show.AdminAssignDelete) // admin/assign/delete 删除指定
	}

	re := e.Group("/admin/recommend/", mw.CheckAccountLogin())
	{

		re.GET("/list", show.AdminRecommendList) // admin/recommend/list 推荐列表

		re.GET("/detail", show.AdminRecommendDetail) // admin/recommend/detail 推荐详情

		re.POST("/create", show.AdminRecommendCreate) // admin/recommend/create 创建推荐

		re.PUT("/update", show.AdminRecommendUpdate) // admin/recommend/update 更新推荐

		re.PATCH("/update", show.AdminRecommendUpdatePatch) // admin/recommend/update 部分更新推荐

		re.DELETE("/delete", show.AdminRecommendDelete) // admin/recommend/delete 删除推荐
	}

	mk := e.Group("/admin/market/", mw.CheckAccountLogin())
	{
		bn := mk.Group("/banner/")
		{
			bn.GET("/list", show.AdminMarketBannerList) // admin/market/banner/list 轮播列表

			bn.GET("/detail", show.AdminMarketBannerDetail) // admin/market/banner/detail 轮播详情

			bn.POST("/create", show.AdminMarketBannerCreate) // admin/market/banner/create 创建轮播

			bn.PUT("/update", show.AdminMarketBannerUpdate) // admin/market/banner/update 更新轮播

			bn.PATCH("/update", show.AdminMarketBannerUpdatePatch) // admin/market/banner/update 部分更新轮播

			bn.DELETE("/delete", show.AdminMarketBannerDelete) // admin/market/banner/delete 删除轮播
		}
	}

	// 关联池
	rl := e.Group("/admin/relation/", mw.CheckAccountLogin())
	{
		rl.GET("/group/list", show.AdminRelationGroupList) // admin/relation/group/list 关联组列表

		rl.POST("/group/create", show.AdminRelationGroupCreate) // admin/relation/group/create 创建关联组

		rl.POST("/group/update", show.AdminRelationGroupUpdate) // admin/relation/group/update 更新关联组

		rl.POST("/group/status/update", show.AdminRelationGroupUpdatePatch) // admin/relation/group/status/update 部分更新关联组

		rl.POST("/group/delete", show.AdminRelationGroupDelete) // admin/relation/group/delete 删除关联组

		rl.POST("/group/member/batch", show.AdminRelationMemberBatch) // admin/relation/group/member/batch 批量设置关联组成员

		rl.GET("/group/show", show.AdminShowRelationList) // admin/relation/group/show 查看剧的关联关系
	}

	// 外部ID映射
	externalIDsHandler := show.NewExternalIDsHandler()
	ei := e.Group("/admin/show/", mw.CheckAccountLogin())
	{
		ei.POST("/:id/sync-external-ids", externalIDsHandler.SyncExternalIDs)        // admin/show/:id/sync-external-ids 同步剧集外部IDs
		ei.GET("/:id/external-ids", externalIDsHandler.GetExternalIDs)               // admin/show/:id/external-ids 获取剧集外部IDs
		ei.PUT("/:id/external-ids", externalIDsHandler.UpdateExternalIDs)            // admin/show/:id/external-ids 更新剧集外部IDs
		ei.POST("/batch-sync-external-ids", externalIDsHandler.BatchSyncExternalIDs) // admin/show/batch-sync-external-ids 批量同步外部IDs
		ei.GET("/search-by-external-id", externalIDsHandler.SearchByExternalID)      // admin/show/search-by-external-id 根据外部ID搜索剧集
		ei.GET("/external-ids/statistics", externalIDsHandler.GetStatistics)         // admin/show/external-ids/statistics 获取外部ID统计信息
	}

}

func loadAdminClassApi(e *gin.RouterGroup) {
	cl := e.Group("/admin/class/", mw.CheckAccountLogin())
	{
		cl.GET("/list", show.AdminClassList) // admin/class/list 类型列表

		cl.GET("/detail", show.AdminClassDetail) // admin/class/detail 类型详情

		cl.POST("/create", show.AdminClassCreate) // admin/class/create 创建类型

		cl.PUT("/update", show.AdminClassUpdate) // admin/class/update 更新类型

		cl.PATCH("/update", show.AdminClassUpdatePatch) // admin/class/update 部分更新类型

		cl.DELETE("/delete", show.AdminClassDelete) // admin/class/delete 删除类型
	}

	cf := e.Group("/admin/class/field/", mw.CheckAccountLogin())
	{
		cf.GET("/list", show.AdminClassFieldList) // admin/class/field/list 类型字段列表

		cf.GET("/detail", show.AdminClassFieldDetail) // admin/class/field/detail 类型字段详情

		cf.POST("/create", show.AdminClassFieldCreate) // admin/class/field/create 创建类型字段

		cf.PUT("/update", show.AdminClassFieldUpdate) // admin/class/field/update 更新类型字段

		cf.PATCH("/update", show.AdminClassFieldUpdatePatch) // admin/class/field/update 部分更新类型字段

		cf.DELETE("/delete", show.AdminClassFieldDelete) // admin/class/field/delete 删除类型字段
	}

	te := e.Group("/admin/test/")
	{

		te.POST("/lok/show/status/update/batch", show.AdminShowStatusBatch) // admin/test/lok/show/status/update/batch 批量更新剧集状态
		te.POST("/lok/to/show", show.AdminTestLokToShow)                    // admin/test/lok/to/show 导入剧集到show
		te.POST("/lok/to/episode", show.AdminTestLokToEpisode)              // admin/test/lok/to/episode 导入剧集到集
	}
}
