package content_show_external_ids

import (
	"database/sql/driver"
	"encoding/json"

	"vlab/app/common/dbs"
)

// ExternalIDs 剧集外部ID映射模型
type ExternalIDs struct {
	dbs.ModelWithDel

	ShowID      uint64          `json:"show_id" gorm:"column:show_id;not null;index:uk_show_id,unique;index:idx_show_id_is_deleted"` // 关联content_show表的ID
	TraktID     *uint64         `json:"trakt_id,omitempty" gorm:"column:trakt_id;index"`                                             // Trakt平台ID
	Slug        string          `json:"slug,omitempty" gorm:"column:slug;size:255;index"`                                            // Slug标识符
	ImdbID      string          `json:"imdb_id,omitempty" gorm:"column:imdb_id;size:20;index"`                                       // IMDB ID
	TmdbID      *uint64         `json:"tmdb_id,omitempty" gorm:"column:tmdb_id;index"`                                               // TMDB ID
	MatchType   string          `json:"match_type,omitempty" gorm:"column:match_type;size:10;index"`                                 // 匹配类型：movie/show
	MatchScore  *float64        `json:"match_score,omitempty" gorm:"column:match_score;type:decimal(5,2)"`                           // 匹配分数(0-100)
	MatchReason string          `json:"match_reason,omitempty" gorm:"column:match_reason;type:text"`                                 // 匹配理由
	IsMatch     uint8           `json:"is_match" gorm:"column:is_match;default:1"`                                                   // 是否有效匹配
	Source      string          `json:"source,omitempty" gorm:"column:source;size:50;default:imdb_api"`                              // 数据来源
	RawResponse *RawResponseMap `json:"raw_response,omitempty" gorm:"column:raw_response;type:json"`                                 // 原始API响应
}

// TableName 指定表名
func (e *ExternalIDs) TableName() string {
	return "content_show_external_ids"
}

// RawResponseMap 用于存储JSON格式的原始响应
type RawResponseMap map[string]interface{}

// Value 实现 driver.Valuer 接口，用于数据库写入
func (r RawResponseMap) Value() (driver.Value, error) {
	if r == nil {
		return nil, nil
	}
	return json.Marshal(r)
}

// Scan 实现 sql.Scanner 接口，用于数据库读取
func (r *RawResponseMap) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	return json.Unmarshal(bytes, r)
}

// MatchTypeEnum 匹配类型枚举
type MatchTypeEnum string

const (
	MatchTypeMovie MatchTypeEnum = "movie" // 电影
	MatchTypeShow  MatchTypeEnum = "show"  // 电视剧
	MatchTypeAuto  MatchTypeEnum = "auto"  // 自动识别
)

// String 转换为字符串
func (m MatchTypeEnum) String() string {
	return string(m)
}

// SourceEnum 数据来源枚举
type SourceEnum string

const (
	SourceIMDBAPI SourceEnum = "imdb_api" // IMDB API
	SourceManual  SourceEnum = "manual"   // 手动录入
	SourceTMDB    SourceEnum = "tmdb"     // TMDB API
	SourceTrakt   SourceEnum = "trakt"    // Trakt API
)

// String 转换为字符串
func (s SourceEnum) String() string {
	return string(s)
}

// Model 别名，保持与其他DAO层一致
type Model = ExternalIDs

// ModelList 模型列表
type ModelList []*Model

// GetShowIDs 获取所有的ShowID
func (list ModelList) GetShowIDs() []uint64 {
	ids := make([]uint64, 0, len(list))
	seen := make(map[uint64]bool)
	for _, item := range list {
		if item != nil && item.ShowID > 0 && !seen[item.ShowID] {
			ids = append(ids, item.ShowID)
			seen[item.ShowID] = true
		}
	}
	return ids
}

// GetImdbIDs 获取所有的IMDB ID
func (list ModelList) GetImdbIDs() []string {
	ids := make([]string, 0, len(list))
	seen := make(map[string]bool)
	for _, item := range list {
		if item != nil && item.ImdbID != "" && !seen[item.ImdbID] {
			ids = append(ids, item.ImdbID)
			seen[item.ImdbID] = true
		}
	}
	return ids
}

// GetTmdbIDs 获取所有的TMDB ID
func (list ModelList) GetTmdbIDs() []uint64 {
	ids := make([]uint64, 0, len(list))
	seen := make(map[uint64]bool)
	for _, item := range list {
		if item != nil && item.TmdbID != nil && *item.TmdbID > 0 && !seen[*item.TmdbID] {
			ids = append(ids, *item.TmdbID)
			seen[*item.TmdbID] = true
		}
	}
	return ids
}

// ToShowIDMap 转换为以ShowID为key的map
func (list ModelList) ToShowIDMap() map[uint64]*Model {
	result := make(map[uint64]*Model, len(list))
	for _, item := range list {
		if item != nil && item.ShowID > 0 {
			result[item.ShowID] = item
		}
	}
	return result
}

// ToImdbIDMap 转换为以ImdbID为key的map
func (list ModelList) ToImdbIDMap() map[string]*Model {
	result := make(map[string]*Model, len(list))
	for _, item := range list {
		if item != nil && item.ImdbID != "" {
			result[item.ImdbID] = item
		}
	}
	return result
}
