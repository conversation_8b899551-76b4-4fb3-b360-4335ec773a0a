# 🚨 紧急修复：解决工具卡住问题

## 当前问题分析
您的工具在显示"断路器状态正常（CLOSED），开始处理任务"后卡住了，这通常是因为：
1. **API调用超时** - IMDB API响应慢或无响应
2. **数据库查询卡住** - 长时间的查询或锁等待
3. **网络问题** - DNS解析或连接超时
4. **内存不足** - GC停顿或内存泄漏

## 🔥 立即解决方案

### 1. 诊断当前卡住的原因

```bash
# 方法1：如果程序还在运行，按 Ctrl+C 然后立即运行诊断
# 如果已经集成了debug_stuck.go，会自动显示诊断信息

# 方法2：手动检查进程
ps aux | grep external_ids_sync
top -p [进程ID]  # 查看CPU和内存使用

# 方法3：如果是Go程序，可以发送SIGQUIT查看goroutine堆栈
kill -QUIT [进程ID]
# 然后查看输出或stderr
```

### 2. 立即停止并重启（推荐）

```bash
# 1. 强制停止当前进程
pkill -f external_ids_sync
# 或者
kill -9 [进程ID]

# 2. 使用防卡住参数重新启动
./external_ids_sync --cmd sync-one --show-id [你的ID] --safe --reset-circuit-breaker

# 或者对于批量处理，使用小批次：
./external_ids_sync --cmd sync-batch --all --page-size 10 --limit-batch 50 --safe
```

### 3. 修改现有代码（立即可用）

在你的 `main.go` 中添加以下修改：

```go
// 在 main 函数开始处添加
func main() {
    // 添加信号处理，程序卡住时按Ctrl+C可以看到诊断信息
    c := make(chan os.Signal, 1)
    signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
    go func() {
        <-c
        fmt.Println("\n🚨 程序被中断，正在诊断...")
        
        // 打印goroutine堆栈
        buf := make([]byte, 64*1024)
        n := runtime.Stack(buf, true)
        fmt.Printf("Goroutine堆栈:\n%s\n", buf[:n])
        
        // 打印内存信息
        var m runtime.MemStats
        runtime.ReadMemStats(&m)
        fmt.Printf("内存使用: %.2f MB\n", float64(m.Alloc)/1024/1024)
        fmt.Printf("Goroutine数量: %d\n", runtime.NumGoroutine())
        
        os.Exit(1)
    }()
    
    // 你的现有代码...
}

// 修改关键的同步函数，添加超时控制
func syncOneWithTimeout(ctx *gin.Context, service *showService.ExternalIDsService) {
    if showID == 0 {
        log.Fatal("请指定 --show-id 参数")
    }

    fmt.Printf("🚀 开始同步剧集 %d（带超时控制）...\n", showID)
    
    // 创建带超时的context
    timeoutCtx, cancel := context.WithTimeout(context.Background(), 3*time.Minute)
    defer cancel()
    
    // 在goroutine中执行同步，这样可以超时控制
    resultChan := make(chan error, 1)
    go func() {
        defer func() {
            if r := recover(); r != nil {
                resultChan <- fmt.Errorf("panic: %v", r)
            }
        }()
        
        fmt.Println("📝 检查现有记录...")
        if !forceUpdate {
            existing, err := service.GetExternalIDsByShowID(ctx, showID)
            if err == nil && existing != nil && existing.IsMatch == 2 {
                fmt.Printf("✅ 剧集 %d 已有外部ID映射\n", showID)
                resultChan <- nil
                return
            }
        }
        
        fmt.Println("📡 调用外部API...")
        startTime := time.Now()
        err := service.SyncExternalIDs(ctx, showID)
        if err != nil {
            resultChan <- fmt.Errorf("API调用失败: %w", err)
            return
        }
        
        fmt.Printf("⚡ API调用完成，耗时: %.2f秒\n", time.Since(startTime).Seconds())
        
        fmt.Println("📊 获取同步结果...")
        result, err := service.GetExternalIDsByShowID(ctx, showID)
        if err != nil {
            resultChan <- fmt.Errorf("获取结果失败: %w", err)
            return
        }
        
        if result != nil {
            printExternalIDs(result)
        }
        
        resultChan <- nil
    }()
    
    // 等待结果或超时
    select {
    case err := <-resultChan:
        if err != nil {
            fmt.Printf("❌ 同步失败: %v\n", err)
            log.Fatalf("同步失败: %v", err)
        } else {
            fmt.Printf("✅ 同步成功！\n")
        }
    case <-timeoutCtx.Done():
        fmt.Printf("⏰ 同步超时（3分钟），可能的原因：\n")
        fmt.Printf("  1. IMDB API响应慢\n")
        fmt.Printf("  2. 网络连接问题\n")
        fmt.Printf("  3. 数据库查询慢\n")
        fmt.Printf("💡 建议：\n")
        fmt.Printf("  1. 检查网络连接\n")
        fmt.Printf("  2. 使用 --reset-circuit-breaker 重置断路器\n")
        fmt.Printf("  3. 使用 --aggressive 模式快速跳过卡住的项目\n")
        
        // 打印当前状态
        var m runtime.MemStats
        runtime.ReadMemStats(&m)
        fmt.Printf("当前内存使用: %.2f MB\n", float64(m.Alloc)/1024/1024)
        fmt.Printf("当前Goroutine数量: %d\n", runtime.NumGoroutine())
        
        log.Fatal("同步超时")
    }
}
```

## 🛡️ 长期防卡住解决方案

### 1. 使用我们设计的完整监控系统

```bash
# 1. 将监控系统集成到你的项目中
cp pkg/monitor/* /path/to/your/project/pkg/monitor/
cp cmd/external_ids_sync/monitor_integration.go /path/to/your/project/cmd/external_ids_sync/

# 2. 在你的main.go中添加监控初始化
# 参考 monitor_integration.go 中的代码

# 3. 使用防卡住模式运行
./external_ids_sync --cmd sync-batch --all --safe --fast
```

### 2. 命令行参数优化

```bash
# 推荐的防卡住参数组合：

# 1. 安全模式（推荐用于不稳定环境）
./external_ids_sync --cmd sync-batch --all --safe --page-size 20

# 2. 激进模式（API频繁卡住时）
./external_ids_sync --cmd sync-batch --all --aggressive --page-size 10

# 3. 调试模式（找出卡住原因）
./external_ids_sync --cmd sync-one --show-id 123 --debug --verbose

# 4. 限制批次大小（避免一次处理太多）
./external_ids_sync --cmd sync-batch --all --limit-batch 100 --page-size 10
```

### 3. 监控关键指标

使用完整监控系统后，重点关注：

```json
// 在监控日志中查找这些指标：
{
  "name": "sync_single.api_call.duration",
  "value": 15.23,  // 如果经常>30秒，说明API慢
  "labels": {"api": "imdb"}
}

{
  "name": "sync_single.database_save.duration", 
  "value": 5.67,   // 如果经常>10秒，说明数据库慢
}

{
  "name": "system.memory.alloc_bytes",
  "value": 524288000  // 如果持续增长，说明内存泄漏
}
```

## 📊 常见卡住原因和解决方案

| 卡住原因 | 症状 | 解决方案 |
|---------|------|---------|
| IMDB API超时 | 在API调用后卡住 | `--aggressive` 模式，使用更短超时 |
| 数据库锁等待 | 在数据库操作后卡住 | 检查数据库连接池，重启数据库 |
| 网络DNS问题 | 程序启动后立即卡住 | 检查DNS设置，使用IP地址 |
| 内存不足 | 处理一段时间后变慢 | 减小 `--page-size`，增加内存 |
| 断路器异常 | 断路器信息后卡住 | 使用 `--reset-circuit-breaker` |
| Goroutine泄漏 | 长时间运行后卡住 | 重启程序，检查并发控制 |

## ⚡ 应急处理流程

1. **立即停止当前进程**
   ```bash
   pkill -f external_ids_sync
   ```

2. **检查系统资源**
   ```bash
   free -h        # 检查内存
   df -h          # 检查磁盘空间
   netstat -tulpn # 检查网络连接
   ```

3. **使用安全模式重启**
   ```bash
   ./external_ids_sync --cmd sync-batch --all --safe --page-size 5 --limit-batch 10
   ```

4. **如果还是卡住，使用单个测试**
   ```bash
   ./external_ids_sync --cmd sync-one --show-id 1 --debug --verbose
   ```

5. **最后手段：激进模式**
   ```bash
   ./external_ids_sync --cmd sync-batch --all --aggressive --page-size 1 --limit-batch 5
   ```

## 💡 预防措施

1. **始终使用超时控制**
2. **监控内存和CPU使用**
3. **使用适当的批次大小**
4. **定期重启长时间运行的任务**
5. **集成完整的监控系统**

---

**下次遇到卡住问题时，立即运行：**
```bash
./external_ids_sync --cmd health-check && \
./external_ids_sync --cmd sync-batch --all --safe --page-size 10 --limit-batch 50
```
