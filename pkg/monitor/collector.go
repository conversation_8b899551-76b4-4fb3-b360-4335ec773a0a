package monitor

import (
	"runtime"
	"runtime/debug"
	"sync"
	"time"
)

// MetricsCollector 指标收集器
type MetricsCollector struct {
	sampler    Sampler
	processID  int
	startTime  time.Time
	counters   map[string]int64
	gauges     map[string]float64
	histograms map[string]*Histogram
	lastValues map[string]interface{}
	mutex      sync.RWMutex

	// 自动收集系统指标
	systemMetricsEnabled  bool
	systemMetricsInterval time.Duration
	systemMetricsTicker   *time.Ticker
	systemMetricsStop     chan struct{}
}

// Histogram 直方图数据结构
type Histogram struct {
	Values []float64
	mutex  sync.RWMutex
}

// Add 添加值到直方图
func (h *Histogram) Add(value float64) {
	h.mutex.Lock()
	defer h.mutex.Unlock()
	h.Values = append(h.Values, value)
}

// GetStats 获取直方图统计信息
func (h *Histogram) GetStats() map[string]float64 {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	if len(h.Values) == 0 {
		return map[string]float64{}
	}

	// 计算基本统计信息
	var sum, min, max float64
	min = h.Values[0]
	max = h.Values[0]

	for _, v := range h.Values {
		sum += v
		if v < min {
			min = v
		}
		if v > max {
			max = v
		}
	}

	avg := sum / float64(len(h.Values))

	return map[string]float64{
		"count": float64(len(h.Values)),
		"sum":   sum,
		"avg":   avg,
		"min":   min,
		"max":   max,
	}
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector(sampler Sampler) *MetricsCollector {
	return &MetricsCollector{
		sampler:               sampler,
		processID:             runtime.GOMAXPROCS(0),
		startTime:             time.Now(),
		counters:              make(map[string]int64),
		gauges:                make(map[string]float64),
		histograms:            make(map[string]*Histogram),
		lastValues:            make(map[string]interface{}),
		systemMetricsEnabled:  false,
		systemMetricsInterval: 30 * time.Second,
		systemMetricsStop:     make(chan struct{}),
	}
}

// EnableSystemMetrics 启用系统指标自动收集
func (mc *MetricsCollector) EnableSystemMetrics(interval time.Duration) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	if mc.systemMetricsEnabled {
		return // 已经启用
	}

	mc.systemMetricsEnabled = true
	mc.systemMetricsInterval = interval
	mc.systemMetricsTicker = time.NewTicker(interval)

	go mc.collectSystemMetrics()
}

// DisableSystemMetrics 禁用系统指标自动收集
func (mc *MetricsCollector) DisableSystemMetrics() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	if !mc.systemMetricsEnabled {
		return
	}

	mc.systemMetricsEnabled = false
	if mc.systemMetricsTicker != nil {
		mc.systemMetricsTicker.Stop()
	}

	close(mc.systemMetricsStop)
	mc.systemMetricsStop = make(chan struct{})
}

// collectSystemMetrics 收集系统指标
func (mc *MetricsCollector) collectSystemMetrics() {
	for {
		select {
		case <-mc.systemMetricsTicker.C:
			mc.recordSystemMetrics()
		case <-mc.systemMetricsStop:
			return
		}
	}
}

// recordSystemMetrics 记录系统指标
func (mc *MetricsCollector) recordSystemMetrics() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 内存指标
	mc.RecordGauge("system.memory.alloc_bytes", float64(m.Alloc))
	mc.RecordGauge("system.memory.total_alloc_bytes", float64(m.TotalAlloc))
	mc.RecordGauge("system.memory.sys_bytes", float64(m.Sys))
	mc.RecordGauge("system.memory.heap_alloc_bytes", float64(m.HeapAlloc))
	mc.RecordGauge("system.memory.heap_sys_bytes", float64(m.HeapSys))
	mc.RecordGauge("system.memory.heap_inuse_bytes", float64(m.HeapInuse))
	mc.RecordGauge("system.memory.heap_released_bytes", float64(m.HeapReleased))
	mc.RecordGauge("system.memory.stack_inuse_bytes", float64(m.StackInuse))
	mc.RecordGauge("system.memory.stack_sys_bytes", float64(m.StackSys))

	// GC 指标
	mc.RecordCounter("system.gc.num_gc", int64(m.NumGC))
	mc.RecordGauge("system.gc.pause_ns", float64(m.PauseNs[(m.NumGC+255)%256]))
	mc.RecordGauge("system.gc.gc_cpu_fraction", m.GCCPUFraction)

	// Goroutine 数量
	mc.RecordGauge("system.goroutines.count", float64(runtime.NumGoroutine()))

	// CPU 核心数
	mc.RecordGauge("system.cpu.num_cpu", float64(runtime.NumCPU()))

	// 运行时长
	mc.RecordGauge("system.runtime.uptime_seconds", float64(time.Since(mc.startTime).Seconds()))
}

// RecordEvent 记录事件
func (mc *MetricsCollector) RecordEvent(name string, severity Severity, ctx map[string]interface{}) {
	data := &SampleData{
		Timestamp:   time.Now(),
		MetricType:  MetricTypeEvent,
		Name:        name,
		Severity:    severity,
		Context:     ctx,
		ProcessID:   mc.processID,
		GoroutineID: getGoroutineID(),
	}

	mc.sampler.Sample(data)
}

// RecordCounter 记录计数器
func (mc *MetricsCollector) RecordCounter(name string, value int64) {
	mc.mutex.Lock()
	mc.counters[name] += value
	currentValue := mc.counters[name]
	mc.mutex.Unlock()

	data := &SampleData{
		Timestamp:   time.Now(),
		MetricType:  MetricTypeCounter,
		Name:        name,
		Value:       currentValue,
		Severity:    SeverityInfo,
		ProcessID:   mc.processID,
		GoroutineID: getGoroutineID(),
	}

	mc.sampler.Sample(data)
}

// RecordGauge 记录仪表盘指标
func (mc *MetricsCollector) RecordGauge(name string, value float64) {
	mc.mutex.Lock()
	mc.gauges[name] = value
	mc.mutex.Unlock()

	data := &SampleData{
		Timestamp:   time.Now(),
		MetricType:  MetricTypeGauge,
		Name:        name,
		Value:       value,
		Severity:    SeverityInfo,
		ProcessID:   mc.processID,
		GoroutineID: getGoroutineID(),
	}

	mc.sampler.Sample(data)
}

// RecordHistogram 记录直方图指标
func (mc *MetricsCollector) RecordHistogram(name string, value float64) {
	mc.mutex.Lock()
	if _, exists := mc.histograms[name]; !exists {
		mc.histograms[name] = &Histogram{
			Values: make([]float64, 0),
		}
	}
	mc.histograms[name].Add(value)
	stats := mc.histograms[name].GetStats()
	mc.mutex.Unlock()

	data := &SampleData{
		Timestamp:   time.Now(),
		MetricType:  MetricTypeHistogram,
		Name:        name,
		Value:       stats,
		Severity:    SeverityInfo,
		ProcessID:   mc.processID,
		GoroutineID: getGoroutineID(),
	}

	mc.sampler.Sample(data)
}

// RecordTiming 记录时间指标
func (mc *MetricsCollector) RecordTiming(name string, duration time.Duration, labels map[string]string) {
	data := &SampleData{
		Timestamp:   time.Now(),
		MetricType:  MetricTypeHistogram,
		Name:        name,
		Value:       duration.Seconds(),
		Duration:    &duration,
		Labels:      labels,
		Severity:    SeverityInfo,
		ProcessID:   mc.processID,
		GoroutineID: getGoroutineID(),
	}

	mc.sampler.Sample(data)

	// 同时记录到直方图
	mc.RecordHistogram(name+".duration_seconds", duration.Seconds())
}

// RecordError 记录错误
func (mc *MetricsCollector) RecordError(name string, err error, ctx map[string]interface{}) {
	stackTrace := ""
	if debug.Stack() != nil {
		stackTrace = string(debug.Stack())
	}

	data := &SampleData{
		Timestamp:   time.Now(),
		MetricType:  MetricTypeEvent,
		Name:        name,
		Error:       err.Error(),
		StackTrace:  stackTrace,
		Context:     ctx,
		Severity:    SeverityError,
		ProcessID:   mc.processID,
		GoroutineID: getGoroutineID(),
	}

	mc.sampler.Sample(data)

	// 同时增加错误计数
	mc.RecordCounter(name+".error_count", 1)
}

// TimeFunc 测量函数执行时间
func (mc *MetricsCollector) TimeFunc(name string, labels map[string]string, fn func() error) error {
	start := time.Now()
	err := fn()
	duration := time.Since(start)

	if err != nil {
		mc.RecordError(name, err, map[string]interface{}{
			"function": name,
			"labels":   labels,
		})
	} else {
		mc.RecordTiming(name, duration, labels)
	}

	return err
}

// GetCurrentStats 获取当前统计信息
func (mc *MetricsCollector) GetCurrentStats() map[string]interface{} {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	stats := make(map[string]interface{})

	// 复制计数器
	counters := make(map[string]int64)
	for k, v := range mc.counters {
		counters[k] = v
	}
	stats["counters"] = counters

	// 复制仪表盘指标
	gauges := make(map[string]float64)
	for k, v := range mc.gauges {
		gauges[k] = v
	}
	stats["gauges"] = gauges

	// 复制直方图统计
	histograms := make(map[string]map[string]float64)
	for k, v := range mc.histograms {
		histograms[k] = v.GetStats()
	}
	stats["histograms"] = histograms

	// 采样器统计
	stats["sampler_stats"] = mc.sampler.GetStats()

	return stats
}

// Close 关闭指标收集器
func (mc *MetricsCollector) Close() error {
	mc.DisableSystemMetrics()
	return mc.sampler.Close()
}

// getGoroutineID 获取当前goroutine ID（简化实现）
func getGoroutineID() int64 {
	// 这是一个简化的实现，实际项目中可以使用更准确的方法
	return int64(runtime.NumGoroutine())
}

// OperationTracker 操作跟踪器
type OperationTracker struct {
	collector *MetricsCollector
	name      string
	labels    map[string]string
	startTime time.Time
	context   map[string]interface{}
}

// NewOperationTracker 创建操作跟踪器
func (mc *MetricsCollector) NewOperationTracker(name string, labels map[string]string, ctx map[string]interface{}) *OperationTracker {
	return &OperationTracker{
		collector: mc,
		name:      name,
		labels:    labels,
		startTime: time.Now(),
		context:   ctx,
	}
}

// Success 标记操作成功
func (ot *OperationTracker) Success() {
	duration := time.Since(ot.startTime)
	ot.collector.RecordTiming(ot.name+".success", duration, ot.labels)
	ot.collector.RecordEvent(ot.name+".completed", SeverityInfo, ot.context)
}

// Error 标记操作失败
func (ot *OperationTracker) Error(err error) {
	duration := time.Since(ot.startTime)
	ot.collector.RecordTiming(ot.name+".error", duration, ot.labels)
	ot.collector.RecordError(ot.name+".failed", err, ot.context)
}

// AddContext 添加上下文信息
func (ot *OperationTracker) AddContext(key string, value interface{}) {
	if ot.context == nil {
		ot.context = make(map[string]interface{})
	}
	ot.context[key] = value
}
