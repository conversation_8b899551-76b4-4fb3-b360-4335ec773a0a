#!/bin/bash

# 🚨 立即修复卡住问题的脚本
# 使用方法: ./fix_stuck_now.sh

echo "🚨 外部ID同步工具 - 立即修复脚本"
echo "=================================="

# 1. 检查是否有卡住的进程
echo "📍 检查是否有运行中的进程..."
RUNNING_PIDS=$(pgrep -f "external_ids_sync")

if [ -n "$RUNNING_PIDS" ]; then
    echo "⚠️  发现运行中的进程: $RUNNING_PIDS"
    
    # 显示进程状态
    echo "📊 进程状态:"
    ps aux | grep external_ids_sync | grep -v grep
    
    # 询问是否终止
    read -p "是否终止这些进程? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🔄 正在终止进程..."
        kill -TERM $RUNNING_PIDS
        sleep 3
        
        # 如果还在运行，强制终止
        STILL_RUNNING=$(pgrep -f "external_ids_sync")
        if [ -n "$STILL_RUNNING" ]; then
            echo "💥 强制终止顽固进程..."
            kill -9 $STILL_RUNNING
        fi
        echo "✅ 进程已终止"
    else
        echo "⏳ 继续监控现有进程..."
    fi
else
    echo "✅ 没有发现运行中的进程"
fi

# 2. 检查系统资源
echo ""
echo "📊 检查系统资源..."
echo "内存使用:"
free -h | head -2

echo ""
echo "磁盘空间:"
df -h | grep -E "(Filesystem|/dev/)"

echo ""
echo "CPU负载:"
uptime

# 3. 检查网络连通性
echo ""
echo "🌐 检查网络连通性..."

# 检查DNS
if nslookup imdb.com > /dev/null 2>&1; then
    echo "✅ DNS解析正常"
else
    echo "❌ DNS解析异常，这可能是卡住的原因"
fi

# 检查IMDB连通性
if curl -s --connect-timeout 10 --max-time 15 "https://imdb.com" > /dev/null; then
    echo "✅ IMDB连通性正常"
else
    echo "❌ IMDB连接异常，这可能是卡住的原因"
fi

# 4. 提供修复选项
echo ""
echo "🛠️  选择修复方案:"
echo "1) 运行健康检查"
echo "2) 安全模式小批量测试 (推荐)"
echo "3) 单个剧集测试"
echo "4) 激进模式快速处理"
echo "5) 查看详细诊断信息"
echo "6) 退出"

read -p "请选择 (1-6): " -n 1 -r
echo

case $REPLY in
    1)
        echo "🔍 运行健康检查..."
        if [ -f "./external_ids_sync" ]; then
            ./external_ids_sync --cmd health-check
        else
            echo "❌ 找不到 external_ids_sync 可执行文件"
            echo "请确保在正确的目录中运行此脚本"
        fi
        ;;
    2)
        echo "🛡️  启动安全模式小批量测试..."
        if [ -f "./external_ids_sync" ]; then
            echo "参数说明:"
            echo "  --safe: 启用安全模式"
            echo "  --page-size 5: 每批只处理5个"
            echo "  --limit-batch 10: 最多处理10个"
            echo "  --reset-circuit-breaker: 重置断路器"
            echo ""
            ./external_ids_sync --cmd sync-batch --all --safe --page-size 5 --limit-batch 10 --reset-circuit-breaker --verbose
        else
            echo "❌ 找不到 external_ids_sync 可执行文件"
        fi
        ;;
    3)
        echo "🔬 单个剧集测试..."
        read -p "请输入要测试的剧集ID: " SHOW_ID
        if [ -n "$SHOW_ID" ] && [ -f "./external_ids_sync" ]; then
            echo "正在测试剧集 $SHOW_ID..."
            timeout 180 ./external_ids_sync --cmd sync-one --show-id "$SHOW_ID" --debug --verbose --reset-circuit-breaker
            if [ $? -eq 124 ]; then
                echo "⏰ 3分钟超时，该剧集处理异常"
            fi
        else
            echo "❌ 无效的剧集ID或找不到可执行文件"
        fi
        ;;
    4)
        echo "🔥 激进模式快速处理..."
        if [ -f "./external_ids_sync" ]; then
            echo "⚠️  激进模式将使用10秒超时，快速跳过卡住的项目"
            read -p "确认启动激进模式? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                ./external_ids_sync --cmd sync-batch --all --aggressive --page-size 3 --limit-batch 20 --reset-circuit-breaker
            else
                echo "已取消"
            fi
        else
            echo "❌ 找不到 external_ids_sync 可执行文件"
        fi
        ;;
    5)
        echo "📋 详细诊断信息..."
        echo ""
        echo "=== 进程信息 ==="
        ps aux | grep -E "(external_ids_sync|PID)" | grep -v grep
        echo ""
        echo "=== 内存详情 ==="
        cat /proc/meminfo | head -10
        echo ""
        echo "=== 网络连接 ==="
        netstat -tulpn | grep -E "(443|80|3306|5432)" | head -5
        echo ""
        echo "=== 磁盘IO ==="
        if command -v iostat > /dev/null; then
            iostat 1 2 | tail -n +4
        else
            echo "iostat 未安装，跳过IO检查"
        fi
        echo ""
        echo "=== 日志文件 ==="
        echo "最近的日志文件:"
        ls -la *.log 2>/dev/null | head -5 || echo "没有找到日志文件"
        ;;
    6)
        echo "👋 退出"
        exit 0
        ;;
    *)
        echo "❌ 无效选择"
        ;;
esac

echo ""
echo "🎯 下次预防卡住的建议:"
echo "1. 始终使用 --safe 或 --fast 模式"
echo "2. 使用小的 --page-size (10-20)"
echo "3. 设置 --limit-batch 限制处理数量"
echo "4. 定期重启长时间运行的任务"
echo "5. 监控内存和网络状况"

echo ""
echo "📖 更多信息请查看: URGENT_FIX.md"
