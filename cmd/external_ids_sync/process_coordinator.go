package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"sync"
	"time"

	"vlab/pkg/redis"
)

// ProcessCoordinator 进程协调器
type ProcessCoordinator struct {
	processID    string
	lockKey      string
	heartbeatKey string
	redisClient  *redis.RedisClient
	logger       *log.Logger
	stopChan     chan struct{}
	wg           sync.WaitGroup
	isLeader     bool
	mu           sync.RWMutex
}

// ProcessInfo 进程信息
type ProcessInfo struct {
	ProcessID   string    `json:"process_id"`
	PID         int       `json:"pid"`
	StartTime   time.Time `json:"start_time"`
	LastSeen    time.Time `json:"last_seen"`
	Status      string    `json:"status"`
	TaskRange   string    `json:"task_range,omitempty"`
	WorkerCount int       `json:"worker_count"`
}

// NewProcessCoordinator 创建进程协调器
func NewProcessCoordinator() *ProcessCoordinator {
	hostname, _ := os.Hostname()
	pid := os.Getpid()
	processID := fmt.Sprintf("%s-%d-%d", hostname, pid, time.Now().Unix())
	
	return &ProcessCoordinator{
		processID:    processID,
		lockKey:      "external_ids_sync:leader_lock",
		heartbeatKey: "external_ids_sync:processes",
		redisClient:  redis.GetRedisClient(),
		logger:       log.New(os.Stdout, "[ProcessCoordinator] ", log.LstdFlags|log.Lmicroseconds),
		stopChan:     make(chan struct{}),
	}
}

// Start 启动协调器
func (pc *ProcessCoordinator) Start(taskRange string, workerCount int) error {
	// 注册进程信息
	if err := pc.registerProcess(taskRange, workerCount); err != nil {
		return fmt.Errorf("注册进程失败: %w", err)
	}
	
	// 尝试获取领导权
	pc.tryBecomeLeader()
	
	// 启动心跳
	pc.wg.Add(1)
	go pc.heartbeatLoop()
	
	// 启动清理任务
	pc.wg.Add(1)
	go pc.cleanupLoop()
	
	pc.logger.Printf("进程协调器已启动，进程ID: %s", pc.processID)
	return nil
}

// Stop 停止协调器
func (pc *ProcessCoordinator) Stop() {
	close(pc.stopChan)
	pc.wg.Wait()
	
	// 清理进程信息
	pc.unregisterProcess()
	
	// 如果是领导者，释放锁
	if pc.isLeader {
		pc.releaseLock()
	}
	
	pc.logger.Printf("进程协调器已停止")
}

// registerProcess 注册进程信息
func (pc *ProcessCoordinator) registerProcess(taskRange string, workerCount int) error {
	processInfo := &ProcessInfo{
		ProcessID:   pc.processID,
		PID:         os.Getpid(),
		StartTime:   time.Now(),
		LastSeen:    time.Now(),
		Status:      "starting",
		TaskRange:   taskRange,
		WorkerCount: workerCount,
	}
	
	data, err := json.Marshal(processInfo)
	if err != nil {
		return err
	}
	
	// 使用Redis Hash存储进程信息
	return pc.redisClient.HSet(context.Background(), pc.heartbeatKey, pc.processID, string(data)).Err()
}

// unregisterProcess 注销进程信息
func (pc *ProcessCoordinator) unregisterProcess() {
	pc.redisClient.HDel(context.Background(), pc.heartbeatKey, pc.processID)
}

// tryBecomeLeader 尝试成为领导者
func (pc *ProcessCoordinator) tryBecomeLeader() {
	// 使用Redis分布式锁
	lockValue := pc.processID
	lockTTL := 60 * time.Second
	
	result := pc.redisClient.SetNX(context.Background(), pc.lockKey, lockValue, lockTTL)
	if result.Err() == nil && result.Val() {
		pc.mu.Lock()
		pc.isLeader = true
		pc.mu.Unlock()
		pc.logger.Printf("🎯 成为领导进程")
	} else {
		pc.logger.Printf("其他进程已是领导者")
	}
}

// releaseLock 释放领导锁
func (pc *ProcessCoordinator) releaseLock() {
	pc.redisClient.Del(context.Background(), pc.lockKey)
	pc.mu.Lock()
	pc.isLeader = false
	pc.mu.Unlock()
	pc.logger.Printf("已释放领导锁")
}

// heartbeatLoop 心跳循环
func (pc *ProcessCoordinator) heartbeatLoop() {
	defer pc.wg.Done()
	
	ticker := time.NewTicker(15 * time.Second) // 每15秒发送心跳
	defer ticker.Stop()
	
	for {
		select {
		case <-pc.stopChan:
			return
		case <-ticker.C:
			pc.sendHeartbeat()
			pc.checkLeadership()
		}
	}
}

// sendHeartbeat 发送心跳
func (pc *ProcessCoordinator) sendHeartbeat() {
	// 更新进程信息
	processInfoStr := pc.redisClient.HGet(context.Background(), pc.heartbeatKey, pc.processID).Val()
	if processInfoStr != "" {
		var processInfo ProcessInfo
		if err := json.Unmarshal([]byte(processInfoStr), &processInfo); err == nil {
			processInfo.LastSeen = time.Now()
			processInfo.Status = "running"
			
			data, err := json.Marshal(processInfo)
			if err == nil {
				pc.redisClient.HSet(context.Background(), pc.heartbeatKey, pc.processID, string(data))
			}
		}
	}
}

// checkLeadership 检查领导权
func (pc *ProcessCoordinator) checkLeadership() {
	pc.mu.RLock()
	isLeader := pc.isLeader
	pc.mu.RUnlock()
	
	if isLeader {
		// 续期领导锁
		lockTTL := 60 * time.Second
		pc.redisClient.Expire(context.Background(), pc.lockKey, lockTTL)
	} else {
		// 尝试获取领导权
		pc.tryBecomeLeader()
	}
}

// cleanupLoop 清理循环
func (pc *ProcessCoordinator) cleanupLoop() {
	defer pc.wg.Done()
	
	ticker := time.NewTicker(60 * time.Second) // 每分钟清理一次
	defer ticker.Stop()
	
	for {
		select {
		case <-pc.stopChan:
			return
		case <-ticker.C:
			pc.mu.RLock()
			isLeader := pc.isLeader
			pc.mu.RUnlock()
			
			if isLeader {
				pc.cleanupDeadProcesses()
			}
		}
	}
}

// cleanupDeadProcesses 清理死进程
func (pc *ProcessCoordinator) cleanupDeadProcesses() {
	processes := pc.redisClient.HGetAll(context.Background(), pc.heartbeatKey).Val()
	now := time.Now()
	
	for processID, processInfoStr := range processes {
		var processInfo ProcessInfo
		if err := json.Unmarshal([]byte(processInfoStr), &processInfo); err != nil {
			continue
		}
		
		// 如果进程超过2分钟没有心跳，认为已死
		if now.Sub(processInfo.LastSeen) > 2*time.Minute {
			pc.logger.Printf("清理死进程: %s (最后心跳: %v)", processID, processInfo.LastSeen)
			pc.redisClient.HDel(context.Background(), pc.heartbeatKey, processID)
		}
	}
}

// GetActiveProcesses 获取活跃进程列表
func (pc *ProcessCoordinator) GetActiveProcesses() ([]*ProcessInfo, error) {
	processes := pc.redisClient.HGetAll(context.Background(), pc.heartbeatKey).Val()
	var activeProcesses []*ProcessInfo
	
	for _, processInfoStr := range processes {
		var processInfo ProcessInfo
		if err := json.Unmarshal([]byte(processInfoStr), &processInfo); err != nil {
			continue
		}
		activeProcesses = append(activeProcesses, &processInfo)
	}
	
	return activeProcesses, nil
}

// IsLeader 检查是否为领导者
func (pc *ProcessCoordinator) IsLeader() bool {
	pc.mu.RLock()
	defer pc.mu.RUnlock()
	return pc.isLeader
}

// GetProcessID 获取进程ID
func (pc *ProcessCoordinator) GetProcessID() string {
	return pc.processID
}

// CheckResourceConflicts 检查资源冲突
func (pc *ProcessCoordinator) CheckResourceConflicts() (*ResourceConflictReport, error) {
	processes, err := pc.GetActiveProcesses()
	if err != nil {
		return nil, err
	}
	
	report := &ResourceConflictReport{
		TotalProcesses: len(processes),
		TotalWorkers:   0,
		Processes:      processes,
		Warnings:       []string{},
	}
	
	// 计算总worker数量
	for _, p := range processes {
		report.TotalWorkers += p.WorkerCount
	}
	
	// 检查潜在冲突
	if report.TotalProcesses > 1 {
		report.Warnings = append(report.Warnings, 
			fmt.Sprintf("检测到%d个并发进程，可能存在资源竞争", report.TotalProcesses))
	}
	
	if report.TotalWorkers > 10 {
		report.Warnings = append(report.Warnings, 
			fmt.Sprintf("总worker数量(%d)较多，可能导致API服务器连接数超限", report.TotalWorkers))
	}
	
	return report, nil
}

// ResourceConflictReport 资源冲突报告
type ResourceConflictReport struct {
	TotalProcesses int            `json:"total_processes"`
	TotalWorkers   int            `json:"total_workers"`
	Processes      []*ProcessInfo `json:"processes"`
	Warnings       []string       `json:"warnings"`
}

// SuggestOptimalConfig 建议最优配置
func (pc *ProcessCoordinator) SuggestOptimalConfig() *OptimalConfig {
	processes, _ := pc.GetActiveProcesses()
	
	config := &OptimalConfig{
		MaxConcurrentProcesses: 2,
		MaxWorkersPerProcess:   3,
		MaxTotalWorkers:        6,
		RecommendedDelay:       time.Second * 2,
	}
	
	if len(processes) > 1 {
		config.MaxWorkersPerProcess = 2 // 多进程时减少worker数量
		config.RecommendedDelay = time.Second * 5 // 增加延迟
	}
	
	return config
}

// OptimalConfig 最优配置
type OptimalConfig struct {
	MaxConcurrentProcesses int           `json:"max_concurrent_processes"`
	MaxWorkersPerProcess   int           `json:"max_workers_per_process"`
	MaxTotalWorkers        int           `json:"max_total_workers"`
	RecommendedDelay       time.Duration `json:"recommended_delay"`
}

// 全局进程协调器
var globalProcessCoordinator *ProcessCoordinator

// InitProcessCoordinator 初始化全局进程协调器
func InitProcessCoordinator() {
	globalProcessCoordinator = NewProcessCoordinator()
}

// GetProcessCoordinator 获取全局进程协调器
func GetProcessCoordinator() *ProcessCoordinator {
	return globalProcessCoordinator
}
