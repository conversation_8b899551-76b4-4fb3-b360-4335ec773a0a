package main

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	"vlab/app/service/show"
)

func main() {
	fmt.Println("========================================")
	fmt.Println("断路器功能测试")
	fmt.Println("========================================\n")

	// 创建断路器配置
	config := &show.CircuitBreakerConfig{
		FailureThreshold:      3,
		SuccessThreshold:      2,
		Timeout:               5 * time.Second,
		MaxHalfOpenRequests:   1,
		WindowSize:            10,
		FailureRateThreshold:  0.5,
		MinRequestsToCalcRate: 5,
		UseExponentialBackoff: true,
		MaxBackoffTime:        30 * time.Second,
	}

	logger := log.New(log.Writer(), "[TEST] ", log.LstdFlags)
	cb := show.NewEnhancedCircuitBreaker(config, logger)

	fmt.Println("1. 测试正常状态（CLOSED）")
	fmt.Printf("   当前状态: %s\n", cb.GetState())

	// 模拟成功请求
	err := cb.Call(context.Background(), func() error {
		fmt.Println("   执行成功请求...")
		return nil
	})
	if err != nil {
		fmt.Printf("   错误: %v\n", err)
	} else {
		fmt.Println("   ✅ 请求成功")
	}

	fmt.Println("\n2. 测试连续失败触发断路器")
	for i := 1; i <= 3; i++ {
		err := cb.Call(context.Background(), func() error {
			fmt.Printf("   执行失败请求 #%d...\n", i)
			return errors.New("模拟API失败")
		})
		if err != nil {
			fmt.Printf("   ❌ 请求失败: %v\n", err)
		}
		fmt.Printf("   当前状态: %s\n", cb.GetState())
	}

	fmt.Println("\n3. 测试断路器开启状态（OPEN）")
	err = cb.Call(context.Background(), func() error {
		return nil
	})
	if err != nil {
		fmt.Printf("   ⛔ 请求被拒绝: %v\n", err)
	}

	fmt.Println("\n4. 等待断路器恢复...")
	fmt.Println("   等待5秒...")
	time.Sleep(5 * time.Second)

	fmt.Println("\n5. 测试半开状态（HALF-OPEN）")
	err = cb.Call(context.Background(), func() error {
		fmt.Println("   执行测试请求...")
		return nil
	})
	if err != nil {
		fmt.Printf("   错误: %v\n", err)
	} else {
		fmt.Println("   ✅ 测试请求成功")
	}
	fmt.Printf("   当前状态: %s\n", cb.GetState())

	// 再次成功以关闭断路器
	err = cb.Call(context.Background(), func() error {
		fmt.Println("   执行第二个成功请求...")
		return nil
	})
	if err != nil {
		fmt.Printf("   错误: %v\n", err)
	} else {
		fmt.Println("   ✅ 请求成功")
	}
	fmt.Printf("   当前状态: %s\n", cb.GetState())

	fmt.Println("\n6. 获取统计信息")
	stats := cb.GetStats()
	fmt.Println("   统计信息:")
	fmt.Printf("   - 状态: %v\n", stats["state"])
	fmt.Printf("   - 总请求数: %v\n", stats["total_requests"])
	fmt.Printf("   - 总失败数: %v\n", stats["total_failures"])
	fmt.Printf("   - 总成功数: %v\n", stats["total_successes"])
	fmt.Printf("   - 失败率: %.2f%%\n", stats["failure_rate"].(float64)*100)

	fmt.Println("\n========================================")
	fmt.Println("测试完成！")
	fmt.Println("========================================")
}