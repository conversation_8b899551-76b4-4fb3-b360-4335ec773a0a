#!/bin/bash

# 外部ID同步诊断脚本
# 用于快速诊断和解决超时问题

echo "=========================================="
echo "🔍 外部ID同步诊断工具"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_network() {
    echo -e "${BLUE}1. 检查网络连接...${NC}"
    
    # 检查到API服务器的连接
    if timeout 10 nc -z 118.196.31.23 5310 2>/dev/null; then
        echo -e "${GREEN}✅ API服务器连接正常${NC}"
    else
        echo -e "${RED}❌ 无法连接到API服务器 (118.196.31.23:5310)${NC}"
        echo -e "${YELLOW}💡 建议检查:${NC}"
        echo "   - 网络连接是否正常"
        echo "   - 防火墙设置"
        echo "   - DNS解析"
        return 1
    fi
    
    # 检查DNS解析
    if nslookup 118.196.31.23 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ DNS解析正常${NC}"
    else
        echo -e "${YELLOW}⚠️  DNS解析可能有问题${NC}"
    fi
    
    return 0
}

check_database() {
    echo -e "${BLUE}2. 检查数据库连接...${NC}"
    
    # 这里需要根据实际配置调整
    # 假设使用MySQL
    if command -v mysql >/dev/null 2>&1; then
        echo -e "${GREEN}✅ MySQL客户端可用${NC}"
        echo -e "${YELLOW}💡 请手动检查数据库连接:${NC}"
        echo "   mysql -h <host> -u <user> -p<password> -e 'SELECT COUNT(*) FROM content_show_external_ids;'"
    else
        echo -e "${YELLOW}⚠️  MySQL客户端未安装，无法测试数据库连接${NC}"
    fi
}

check_system_resources() {
    echo -e "${BLUE}3. 检查系统资源...${NC}"
    
    # 检查内存使用
    memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    if (( $(echo "$memory_usage > 90" | bc -l) )); then
        echo -e "${RED}❌ 内存使用率过高: ${memory_usage}%${NC}"
    else
        echo -e "${GREEN}✅ 内存使用正常: ${memory_usage}%${NC}"
    fi
    
    # 检查磁盘空间
    disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 90 ]; then
        echo -e "${RED}❌ 磁盘空间不足: ${disk_usage}%${NC}"
    else
        echo -e "${GREEN}✅ 磁盘空间充足: ${disk_usage}%${NC}"
    fi
    
    # 检查负载
    load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    cpu_cores=$(nproc)
    if (( $(echo "$load_avg > $cpu_cores" | bc -l) )); then
        echo -e "${YELLOW}⚠️  系统负载较高: ${load_avg} (CPU核心数: ${cpu_cores})${NC}"
    else
        echo -e "${GREEN}✅ 系统负载正常: ${load_avg}${NC}"
    fi
}

test_api_response() {
    echo -e "${BLUE}4. 测试API响应时间...${NC}"
    
    # 测试API响应时间
    start_time=$(date +%s.%N)
    if timeout 30 curl -s "http://118.196.31.23:5310" >/dev/null 2>&1; then
        end_time=$(date +%s.%N)
        response_time=$(echo "$end_time - $start_time" | bc)
        echo -e "${GREEN}✅ API响应时间: ${response_time}秒${NC}"
        
        if (( $(echo "$response_time > 10" | bc -l) )); then
            echo -e "${YELLOW}⚠️  API响应较慢，可能影响同步性能${NC}"
        fi
    else
        echo -e "${RED}❌ API请求超时或失败${NC}"
        return 1
    fi
    
    return 0
}

suggest_solutions() {
    echo -e "${BLUE}5. 解决方案建议...${NC}"
    
    echo -e "${YELLOW}🔧 基于诊断结果的建议:${NC}"
    
    if ! check_network; then
        echo "   - 网络问题: 检查网络连接和防火墙设置"
        echo "   - 使用 --safe 模式: ./external_ids_sync --cmd sync-batch --all --safe"
    fi
    
    echo ""
    echo -e "${YELLOW}📋 常用修复命令:${NC}"
    echo "   # 健康检查"
    echo "   ./external_ids_sync --cmd health-check"
    echo ""
    echo "   # 重置断路器"
    echo "   ./external_ids_sync --cmd sync-batch --all --reset-circuit-breaker"
    echo ""
    echo "   # 安全模式同步（推荐用于网络不稳定环境）"
    echo "   ./external_ids_sync --cmd sync-batch --all --safe"
    echo ""
    echo "   # 激进模式（快速跳过卡住的剧集）"
    echo "   ./external_ids_sync --cmd sync-batch --all --aggressive"
    echo ""
    echo "   # 单个剧集调试"
    echo "   ./external_ids_sync --cmd sync-one --show-id <ID> --debug"
}

# 主函数
main() {
    echo "开始诊断..."
    echo ""
    
    check_network
    echo ""
    
    check_database
    echo ""
    
    check_system_resources
    echo ""
    
    test_api_response
    echo ""
    
    suggest_solutions
    echo ""
    
    echo "=========================================="
    echo -e "${GREEN}🎉 诊断完成${NC}"
    echo "=========================================="
}

# 检查依赖
check_dependencies() {
    missing_deps=()
    
    if ! command -v nc >/dev/null 2>&1; then
        missing_deps+=("netcat")
    fi
    
    if ! command -v curl >/dev/null 2>&1; then
        missing_deps+=("curl")
    fi
    
    if ! command -v bc >/dev/null 2>&1; then
        missing_deps+=("bc")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${RED}❌ 缺少依赖: ${missing_deps[*]}${NC}"
        echo "请安装缺少的依赖后重新运行"
        exit 1
    fi
}

# 运行诊断
check_dependencies
main
