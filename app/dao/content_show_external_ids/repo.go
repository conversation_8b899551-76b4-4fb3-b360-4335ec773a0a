package content_show_external_ids

import (
	"errors"
	"sync"
	"time"

	"vlab/app/common/dbs"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Repo 数据访问接口
type Repo interface {
	// 基础CRUD操作
	Create(ctx *gin.Context, model *Model) error
	Update(ctx *gin.Context, model *Model) error
	Delete(ctx *gin.Context, id uint64) error
	SoftDelete(ctx *gin.Context, id uint64) error
	FindByID(ctx *gin.Context, id uint64) (*Model, error)
	FindByFilter(ctx *gin.Context, filter *Filter) (ModelList, error)
	FindOneByFilter(ctx *gin.Context, filter *Filter) (*Model, error)
	Count(ctx *gin.Context, filter *Filter) (int64, error)

	// 批量操作
	BatchCreate(ctx *gin.Context, models ModelList) error
	BatchUpdate(ctx *gin.Context, models ModelList) error
	BatchSoftDelete(ctx *gin.Context, ids []uint64) error

	// 特定查询
	FindByShowID(ctx *gin.Context, showID uint64) (*Model, error)
	FindByShowIDs(ctx *gin.Context, showIDs []uint64) (ModelList, error)
	FindByImdbID(ctx *gin.Context, imdbID string) (*Model, error)
	FindByTmdbID(ctx *gin.Context, tmdbID uint64) (*Model, error)
	UpsertByShowID(ctx *gin.Context, model *Model) error
}

// Entry 实现Repo接口
type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

// newEntry 创建新的Entry实例
func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}

// Create 创建记录
func (e Entry) Create(ctx *gin.Context, model *Model) error {
	if model == nil {
		return errors.New("model cannot be nil")
	}
	return e.MysqlEngine.UseWithGinCtx(ctx, true).Create(model).Error
}

// Update 更新记录
func (e Entry) Update(ctx *gin.Context, model *Model) error {
	if model == nil || model.ID == 0 {
		return errors.New("invalid model or id")
	}
	return e.MysqlEngine.UseWithGinCtx(ctx, true).Model(model).Where("id = ?", model.ID).Updates(model).Error
}

// Delete 物理删除记录
func (e Entry) Delete(ctx *gin.Context, id uint64) error {
	if id == 0 {
		return errors.New("invalid id")
	}
	return e.MysqlEngine.UseWithGinCtx(ctx, true).Delete(&Model{}, id).Error
}

// SoftDelete 软删除记录
func (e Entry) SoftDelete(ctx *gin.Context, id uint64) error {
	if id == 0 {
		return errors.New("invalid id")
	}
	return e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("id = ?", id).Update("is_deleted", 1).Error
}

// FindByID 根据ID查询
func (e Entry) FindByID(ctx *gin.Context, id uint64) (*Model, error) {
	if id == 0 {
		return nil, errors.New("invalid id")
	}
	var model Model
	err := e.MysqlEngine.UseWithGinCtx(ctx, true).Where("id = ? AND is_deleted = 0", id).First(&model).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// FindByFilter 根据条件查询列表
func (e Entry) FindByFilter(ctx *gin.Context, filter *Filter) (ModelList, error) {
	if filter == nil {
		filter = &Filter{}
	}
	var models ModelList
	query := filter.BuildQuery(e.MysqlEngine.UseWithGinCtx(ctx, true))
	err := query.Find(&models).Error
	if err != nil {
		return nil, err
	}
	return models, nil
}

// FindOneByFilter 根据条件查询单条记录
func (e Entry) FindOneByFilter(ctx *gin.Context, filter *Filter) (*Model, error) {
	if filter == nil {
		filter = &Filter{}
	}
	var model Model
	query := filter.BuildQuery(e.MysqlEngine.UseWithGinCtx(ctx, true))
	err := query.First(&model).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// Count 统计数量
func (e Entry) Count(ctx *gin.Context, filter *Filter) (int64, error) {
	if filter == nil {
		filter = &Filter{}
	}
	var count int64
	query := filter.BuildQuery(e.MysqlEngine.UseWithGinCtx(ctx, true))
	err := query.Count(&count).Error
	return count, err
}

// BatchCreate 批量创建
func (e Entry) BatchCreate(ctx *gin.Context, models ModelList) error {
	if len(models) == 0 {
		return nil
	}
	return e.MysqlEngine.UseWithGinCtx(ctx, true).CreateInBatches(models, 100).Error
}

// BatchUpdate 批量更新
func (e Entry) BatchUpdate(ctx *gin.Context, models ModelList) error {
	if len(models) == 0 {
		return nil
	}

	tx := e.MysqlEngine.UseWithGinCtx(ctx, true).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, model := range models {
		if model.ID == 0 {
			continue
		}
		if err := tx.Model(model).Where("id = ?", model.ID).Updates(model).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// BatchSoftDelete 批量软删除
func (e Entry) BatchSoftDelete(ctx *gin.Context, ids []uint64) error {
	if len(ids) == 0 {
		return nil
	}
	return e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("id IN ?", ids).Update("is_deleted", 1).Error
}

// FindByShowID 根据ShowID查询
func (e Entry) FindByShowID(ctx *gin.Context, showID uint64) (*Model, error) {
	if showID == 0 {
		return nil, errors.New("invalid show_id")
	}
	var model Model
	err := e.MysqlEngine.UseWithGinCtx(ctx, true).Where("show_id = ? AND is_deleted = 0", showID).First(&model).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// FindByShowIDs 根据多个ShowID查询
func (e Entry) FindByShowIDs(ctx *gin.Context, showIDs []uint64) (ModelList, error) {
	if len(showIDs) == 0 {
		return nil, nil
	}
	var models ModelList
	err := e.MysqlEngine.UseWithGinCtx(ctx, true).Where("show_id IN ? AND is_deleted = 0", showIDs).Find(&models).Error
	if err != nil {
		return nil, err
	}
	return models, nil
}

// FindByImdbID 根据IMDB ID查询
func (e Entry) FindByImdbID(ctx *gin.Context, imdbID string) (*Model, error) {
	if imdbID == "" {
		return nil, errors.New("invalid imdb_id")
	}
	var model Model
	err := e.MysqlEngine.UseWithGinCtx(ctx, true).Where("imdb_id = ? AND is_deleted = 0", imdbID).First(&model).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// FindByTmdbID 根据TMDB ID查询
func (e Entry) FindByTmdbID(ctx *gin.Context, tmdbID uint64) (*Model, error) {
	if tmdbID == 0 {
		return nil, errors.New("invalid tmdb_id")
	}
	var model Model
	err := e.MysqlEngine.UseWithGinCtx(ctx, true).Where("tmdb_id = ? AND is_deleted = 0", tmdbID).First(&model).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// UpsertByShowID 根据ShowID更新或插入
func (e Entry) UpsertByShowID(ctx *gin.Context, model *Model) error {
	if model == nil || model.ShowID == 0 {
		return errors.New("invalid model or show_id")
	}

	// 先查询是否存在
	existing, err := e.FindByShowID(ctx, model.ShowID)
	if err != nil {
		return err
	}

	if existing != nil {
		// 存在则更新
		model.ID = existing.ID
		model.CreatedAt = existing.CreatedAt
		now := dbs.LocalTime(time.Now())
		model.UpdatedAt = &now
		return e.Update(ctx, model)
	}

	// 不存在则创建
	return e.Create(ctx, model)
}

// 全局Repo实例
var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

// GetRepo 获取默认Repo
func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}
