package monitor

import (
	"fmt"
	"sync"
	"time"
)

// SamplingStrategy 采样策略
type SamplingStrategy interface {
	ShouldSample(data interface{}) bool
	GetName() string
}

// MetricType 指标类型
type MetricType string

const (
	MetricTypeCounter   MetricType = "counter"
	MetricTypeGauge     MetricType = "gauge"
	MetricTypeHistogram MetricType = "histogram"
	MetricTypeEvent     MetricType = "event"
)

// Severity 严重程度
type Severity string

const (
	SeverityDebug    Severity = "debug"
	SeverityInfo     Severity = "info"
	SeverityWarn     Severity = "warn"
	SeverityError    Severity = "error"
	SeverityCritical Severity = "critical"
)

// SampleData 采样数据结构
type SampleData struct {
	Timestamp   time.Time              `json:"timestamp"`
	MetricType  MetricType             `json:"metric_type"`
	Name        string                 `json:"name"`
	Value       interface{}            `json:"value"`
	Labels      map[string]string      `json:"labels,omitempty"`
	Severity    Severity               `json:"severity"`
	Context     map[string]interface{} `json:"context,omitempty"`
	Duration    *time.Duration         `json:"duration,omitempty"`
	Error       string                 `json:"error,omitempty"`
	StackTrace  string                 `json:"stack_trace,omitempty"`
	RequestID   string                 `json:"request_id,omitempty"`
	ProcessID   int                    `json:"process_id"`
	GoroutineID int64                  `json:"goroutine_id,omitempty"`
}

// Sampler 采样器接口
type Sampler interface {
	Sample(data *SampleData) error
	Flush() error
	Close() error
	SetStrategy(strategy SamplingStrategy)
	GetStats() SamplerStats
}

// SamplerStats 采样器统计信息
type SamplerStats struct {
	TotalSamples    int64     `json:"total_samples"`
	AcceptedSamples int64     `json:"accepted_samples"`
	DroppedSamples  int64     `json:"dropped_samples"`
	ErrorCount      int64     `json:"error_count"`
	LastSampleTime  time.Time `json:"last_sample_time"`
}

// CompositeSampler 组合采样器，支持多种输出
type CompositeSampler struct {
	samplers []Sampler
	strategy SamplingStrategy
	stats    SamplerStats
	mutex    sync.RWMutex
	closed   bool
}

// NewCompositeSampler 创建组合采样器
func NewCompositeSampler() *CompositeSampler {
	return &CompositeSampler{
		samplers: make([]Sampler, 0),
		strategy: &AlwaysSample{}, // 默认采样策略
	}
}

// AddSampler 添加采样器
func (cs *CompositeSampler) AddSampler(sampler Sampler) {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	if !cs.closed {
		cs.samplers = append(cs.samplers, sampler)
	}
}

// SetStrategy 设置采样策略
func (cs *CompositeSampler) SetStrategy(strategy SamplingStrategy) {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	cs.strategy = strategy
	for _, sampler := range cs.samplers {
		sampler.SetStrategy(strategy)
	}
}

// Sample 执行采样
func (cs *CompositeSampler) Sample(data *SampleData) error {
	cs.mutex.RLock()
	defer cs.mutex.RUnlock()

	if cs.closed {
		return fmt.Errorf("sampler is closed")
	}

	// 更新统计信息
	cs.stats.TotalSamples++
	cs.stats.LastSampleTime = time.Now()

	// 检查是否应该采样
	if !cs.strategy.ShouldSample(data) {
		cs.stats.DroppedSamples++
		return nil
	}

	cs.stats.AcceptedSamples++

	// 发送到所有采样器
	var lastErr error
	for _, sampler := range cs.samplers {
		if err := sampler.Sample(data); err != nil {
			cs.stats.ErrorCount++
			lastErr = err
		}
	}

	return lastErr
}

// Flush 刷新所有采样器
func (cs *CompositeSampler) Flush() error {
	cs.mutex.RLock()
	defer cs.mutex.RUnlock()

	var lastErr error
	for _, sampler := range cs.samplers {
		if err := sampler.Flush(); err != nil {
			lastErr = err
		}
	}

	return lastErr
}

// Close 关闭所有采样器
func (cs *CompositeSampler) Close() error {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	if cs.closed {
		return nil
	}

	var lastErr error
	for _, sampler := range cs.samplers {
		if err := sampler.Close(); err != nil {
			lastErr = err
		}
	}

	cs.closed = true
	return lastErr
}

// GetStats 获取统计信息
func (cs *CompositeSampler) GetStats() SamplerStats {
	cs.mutex.RLock()
	defer cs.mutex.RUnlock()

	// 合并所有采样器的统计信息
	combinedStats := cs.stats
	for _, sampler := range cs.samplers {
		stats := sampler.GetStats()
		combinedStats.ErrorCount += stats.ErrorCount
	}

	return combinedStats
}

// AlwaysSample 总是采样策略
type AlwaysSample struct{}

func (a *AlwaysSample) ShouldSample(data interface{}) bool {
	return true
}

func (a *AlwaysSample) GetName() string {
	return "always_sample"
}

// RateLimitSample 速率限制采样策略
type RateLimitSample struct {
	maxSamplesPerSecond int64
	lastReset           time.Time
	currentCount        int64
	mutex               sync.Mutex
}

// NewRateLimitSample 创建速率限制采样策略
func NewRateLimitSample(maxSamplesPerSecond int64) *RateLimitSample {
	return &RateLimitSample{
		maxSamplesPerSecond: maxSamplesPerSecond,
		lastReset:           time.Now(),
	}
}

func (r *RateLimitSample) ShouldSample(data interface{}) bool {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	now := time.Now()
	if now.Sub(r.lastReset) >= time.Second {
		r.currentCount = 0
		r.lastReset = now
	}

	if r.currentCount < r.maxSamplesPerSecond {
		r.currentCount++
		return true
	}

	return false
}

func (r *RateLimitSample) GetName() string {
	return fmt.Sprintf("rate_limit_%d_per_second", r.maxSamplesPerSecond)
}

// PercentageSample 百分比采样策略
type PercentageSample struct {
	percentage float64
	counter    int64
	mutex      sync.Mutex
}

// NewPercentageSample 创建百分比采样策略（0.0-1.0）
func NewPercentageSample(percentage float64) *PercentageSample {
	if percentage < 0 {
		percentage = 0
	}
	if percentage > 1 {
		percentage = 1
	}

	return &PercentageSample{
		percentage: percentage,
	}
}

func (p *PercentageSample) ShouldSample(data interface{}) bool {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.counter++

	// 使用计数器来实现百分比采样
	threshold := int64(1.0 / p.percentage)
	return p.counter%threshold == 0
}

func (p *PercentageSample) GetName() string {
	return fmt.Sprintf("percentage_%.2f", p.percentage*100)
}

// SeveritySample 基于严重程度的采样策略
type SeveritySample struct {
	minSeverity Severity
}

// NewSeveritySample 创建基于严重程度的采样策略
func NewSeveritySample(minSeverity Severity) *SeveritySample {
	return &SeveritySample{
		minSeverity: minSeverity,
	}
}

func (s *SeveritySample) ShouldSample(data interface{}) bool {
	if sampleData, ok := data.(*SampleData); ok {
		return s.severityLevel(sampleData.Severity) >= s.severityLevel(s.minSeverity)
	}
	return true
}

func (s *SeveritySample) severityLevel(severity Severity) int {
	switch severity {
	case SeverityDebug:
		return 0
	case SeverityInfo:
		return 1
	case SeverityWarn:
		return 2
	case SeverityError:
		return 3
	case SeverityCritical:
		return 4
	default:
		return 0
	}
}

func (s *SeveritySample) GetName() string {
	return fmt.Sprintf("severity_min_%s", s.minSeverity)
}

// ConditionalSample 条件采样策略
type ConditionalSample struct {
	condition func(data interface{}) bool
	name      string
}

// NewConditionalSample 创建条件采样策略
func NewConditionalSample(name string, condition func(data interface{}) bool) *ConditionalSample {
	return &ConditionalSample{
		condition: condition,
		name:      name,
	}
}

func (c *ConditionalSample) ShouldSample(data interface{}) bool {
	return c.condition(data)
}

func (c *ConditionalSample) GetName() string {
	return c.name
}
