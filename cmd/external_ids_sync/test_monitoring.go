package main

import (
	"fmt"
	"time"

	"vlab/pkg/monitor"
)

func testMonitoring() {
	fmt.Println("🧪 测试监控系统...")

	// 使用全局监控系统
	if globalMonitor == nil {
		fmt.Println("❌ 全局监控系统未初始化")
		return
	}

	fmt.Println("✅ 使用全局监控系统进行测试")

	// 记录一些测试事件
	globalMonitor.RecordEvent("test.started", monitor.SeverityInfo, map[string]interface{}{
		"test_name": "monitoring_test",
		"timestamp": time.Now(),
	})

	globalMonitor.RecordCounter("test.counter", 1)
	globalMonitor.RecordGauge("test.gauge", 42.5)

	// 测试时间测量
	err := globalMonitor.TimeFunc("test.function", map[string]string{"operation": "test"}, func() error {
		time.Sleep(100 * time.Millisecond)
		return nil
	})

	if err != nil {
		fmt.Printf("❌ 时间测量测试失败: %v\n", err)
	} else {
		fmt.Println("✅ 时间测量测试成功")
	}

	// 记录完成事件
	globalMonitor.RecordEvent("test.completed", monitor.SeverityInfo, map[string]interface{}{
		"result":   "success",
		"duration": "100ms",
	})

	fmt.Println("✅ 监控系统测试完成")
}
