package imdb

// MatchRequest IMDB匹配请求结构
type MatchRequest struct {
	Type     string   `json:"type" binding:"required,oneof=movie show auto"` // 内容类型：movie/show/auto
	Title    string   `json:"title"`                                         // 剧集名称
	Year     int      `json:"year"`                                          // 年份
	Overview string   `json:"overview"`                                      // 剧集简介
	Language string   `json:"language"`                                      // 语言代码
	Genres   []string `json:"genres"`                                        // 类型标签数组
}

// MatchResponse IMDB匹配响应结构
type MatchResponse struct {
	Code int        `json:"code"`
	Msg  string     `json:"msg"`
	Data *MatchData `json:"data"`
}

// MatchData 匹配数据
type MatchData struct {
	Match      *MatchWrapper `json:"match"`       // 匹配结果包装器（新版本API）
	Movie      *MovieResult  `json:"movie"`       // 电影匹配结果（旧版本兼容）
	Show       *ShowResult   `json:"show"`        // 剧集匹配结果（旧版本兼容）
	Reason     string        `json:"reason"`      // 匹配理由
	MatchScore float64       `json:"match_score"` // 匹配分数
	IsMatch    bool          `json:"is_match"`    // 是否匹配成功
}

// MatchWrapper 匹配结果包装器（新版本API的match字段）
type MatchWrapper struct {
	Type  string       `json:"type"`  // movie/show
	Score float64      `json:"score"` // 内部评分
	Movie *MovieResult `json:"movie"` // 电影详情
	Show  *ShowResult  `json:"show"`  // 剧集详情
}

// MatchResult 匹配结果详情（旧版本兼容，保留但已废弃）
type MatchResult struct {
	Type          string    `json:"type"`                     // movie/show
	Title         string    `json:"title"`                    // 标题
	Year          int       `json:"year"`                     // 年份
	IDs           *MediaIDs `json:"ids"`                      // 外部ID集合
	Tagline       string    `json:"tagline,omitempty"`        // 标语
	Overview      string    `json:"overview,omitempty"`       // 简介
	Released      string    `json:"released,omitempty"`       // 发布日期
	Runtime       int       `json:"runtime,omitempty"`        // 时长（分钟）
	Country       string    `json:"country,omitempty"`        // 国家
	Language      string    `json:"language,omitempty"`       // 语言
	Genres        []string  `json:"genres,omitempty"`         // 类型
	Certification string    `json:"certification,omitempty"`  // 分级
	OriginalTitle string    `json:"original_title,omitempty"` // 原标题
	Score         float64   `json:"score,omitempty"`          // 评分
}

// MovieResult 电影匹配结果（新版本API）
type MovieResult struct {
	Title         string    `json:"title"`                    // 标题
	Year          int       `json:"year"`                     // 年份
	IDs           *MediaIDs `json:"ids"`                      // 外部ID集合
	Tagline       string    `json:"tagline,omitempty"`        // 标语
	Overview      string    `json:"overview,omitempty"`       // 简介
	Released      string    `json:"released,omitempty"`       // 发布日期
	Runtime       int       `json:"runtime,omitempty"`        // 时长（分钟）
	Country       string    `json:"country,omitempty"`        // 国家
	Language      string    `json:"language,omitempty"`       // 语言
	Genres        []string  `json:"genres,omitempty"`         // 类型
	Certification string    `json:"certification,omitempty"`  // 分级
	OriginalTitle string    `json:"original_title,omitempty"` // 原标题
}

// ShowResult 剧集匹配结果（新版本API）
type ShowResult struct {
	Title         string    `json:"title"`                    // 标题
	Year          int       `json:"year"`                     // 年份
	IDs           *MediaIDs `json:"ids"`                      // 外部ID集合
	Tagline       string    `json:"tagline,omitempty"`        // 标语
	Overview      string    `json:"overview,omitempty"`       // 简介
	Released      string    `json:"released,omitempty"`       // 发布日期
	Runtime       int       `json:"runtime,omitempty"`        // 时长（分钟）
	Country       string    `json:"country,omitempty"`        // 国家
	Language      string    `json:"language,omitempty"`       // 语言
	Genres        []string  `json:"genres,omitempty"`         // 类型
	Certification string    `json:"certification,omitempty"`  // 分级
	OriginalTitle string    `json:"original_title,omitempty"` // 原标题
}

// MediaIDs 媒体外部ID集合
type MediaIDs struct {
	Trakt uint64 `json:"trakt"` // Trakt ID
	Slug  string `json:"slug"`  // Slug标识符
	IMDB  string `json:"imdb"`  // IMDB ID
	TMDB  uint64 `json:"tmdb"`  // TMDB ID
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// MatchConfig API配置
type MatchConfig struct {
	BaseURL    string  // 基础URL
	Timeout    int     // 超时时间（秒）
	MaxRetries int     // 最大重试次数
	MinScore   float64 // 最小匹配分数（默认90）
}

// DefaultConfig 默认配置
func DefaultConfig() *MatchConfig {
	return &MatchConfig{
		BaseURL:    "http://118.196.31.23:5310",
		Timeout:    30, // 增加超时时间到30秒，给网络延迟留出空间
		MaxRetries: 2,  // 增加重试次数到2次，提高成功率
		MinScore:   90.0,
	}
}
