package monitor

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"sync"
	"time"
)

// FileSampler 文件采样器
type FileSampler struct {
	filepath      string
	file          *os.File
	writer        *bufio.Writer
	strategy      SamplingStrategy
	stats         SamplerStats
	mutex         sync.Mutex
	flushInterval time.Duration
	lastFlush     time.Time
	closed        bool
}

// NewFileSampler 创建文件采样器
func NewFileSampler(filePath string) (*FileSampler, error) {
	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create directory: %w", err)
	}

	file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}

	fs := &FileSampler{
		filepath:      filePath,
		file:          file,
		writer:        bufio.NewWriter(file),
		strategy:      &AlwaysSample{},
		flushInterval: 5 * time.Second,
		lastFlush:     time.Now(),
	}

	return fs, nil
}

// SetStrategy 设置采样策略
func (fs *FileSampler) SetStrategy(strategy SamplingStrategy) {
	fs.mutex.Lock()
	defer fs.mutex.Unlock()
	fs.strategy = strategy
}

// Sample 写入采样数据
func (fs *FileSampler) Sample(data *SampleData) error {
	fs.mutex.Lock()
	defer fs.mutex.Unlock()

	if fs.closed {
		return fmt.Errorf("file sampler is closed")
	}

	// 检查是否应该采样
	if !fs.strategy.ShouldSample(data) {
		fs.stats.DroppedSamples++
		return nil
	}

	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		fs.stats.ErrorCount++
		return fmt.Errorf("failed to marshal data: %w", err)
	}

	// 写入文件
	if _, err := fs.writer.Write(jsonData); err != nil {
		fs.stats.ErrorCount++
		return fmt.Errorf("failed to write data: %w", err)
	}

	if _, err := fs.writer.WriteString("\n"); err != nil {
		fs.stats.ErrorCount++
		return fmt.Errorf("failed to write newline: %w", err)
	}

	fs.stats.TotalSamples++
	fs.stats.AcceptedSamples++
	fs.stats.LastSampleTime = time.Now()

	// 定期自动刷新
	if time.Since(fs.lastFlush) > fs.flushInterval {
		fs.writer.Flush()
		fs.lastFlush = time.Now()
	}

	return nil
}

// Flush 刷新缓冲区
func (fs *FileSampler) Flush() error {
	fs.mutex.Lock()
	defer fs.mutex.Unlock()

	if fs.writer != nil {
		err := fs.writer.Flush()
		if err == nil {
			err = fs.file.Sync()
		}
		fs.lastFlush = time.Now()
		return err
	}
	return nil
}

// Close 关闭文件采样器
func (fs *FileSampler) Close() error {
	fs.mutex.Lock()
	defer fs.mutex.Unlock()

	if fs.closed {
		return nil
	}

	var err error
	if fs.writer != nil {
		fs.writer.Flush()
		fs.writer = nil
	}

	if fs.file != nil {
		err = fs.file.Close()
		fs.file = nil
	}

	fs.closed = true
	return err
}

// GetStats 获取统计信息
func (fs *FileSampler) GetStats() SamplerStats {
	fs.mutex.Lock()
	defer fs.mutex.Unlock()
	return fs.stats
}

// ConsoleSampler 控制台采样器
type ConsoleSampler struct {
	strategy SamplingStrategy
	stats    SamplerStats
	mutex    sync.Mutex
	colored  bool
	closed   bool
}

// NewConsoleSampler 创建控制台采样器
func NewConsoleSampler(colored bool) *ConsoleSampler {
	return &ConsoleSampler{
		strategy: &AlwaysSample{},
		colored:  colored,
	}
}

// SetStrategy 设置采样策略
func (cs *ConsoleSampler) SetStrategy(strategy SamplingStrategy) {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()
	cs.strategy = strategy
}

// Sample 输出采样数据到控制台
func (cs *ConsoleSampler) Sample(data *SampleData) error {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()

	if cs.closed {
		return fmt.Errorf("console sampler is closed")
	}

	// 检查是否应该采样
	if !cs.strategy.ShouldSample(data) {
		cs.stats.DroppedSamples++
		return nil
	}

	// 格式化输出
	timestamp := data.Timestamp.Format("2006-01-02 15:04:05.000")

	var output string
	if cs.colored {
		output = cs.formatColoredOutput(timestamp, data)
	} else {
		output = cs.formatPlainOutput(timestamp, data)
	}

	fmt.Print(output)

	cs.stats.TotalSamples++
	cs.stats.AcceptedSamples++
	cs.stats.LastSampleTime = time.Now()

	return nil
}

// formatColoredOutput 格式化彩色输出
func (cs *ConsoleSampler) formatColoredOutput(timestamp string, data *SampleData) string {
	var severityColor string
	switch data.Severity {
	case SeverityDebug:
		severityColor = "\033[36m" // 青色
	case SeverityInfo:
		severityColor = "\033[32m" // 绿色
	case SeverityWarn:
		severityColor = "\033[33m" // 黄色
	case SeverityError:
		severityColor = "\033[31m" // 红色
	case SeverityCritical:
		severityColor = "\033[35m" // 紫色
	default:
		severityColor = "\033[0m" // 重置
	}

	resetColor := "\033[0m"

	output := fmt.Sprintf("%s[%s] %s%s%s [%s] %s",
		"\033[90m", // 灰色时间戳
		timestamp,
		severityColor,
		data.Severity,
		resetColor,
		data.MetricType,
		data.Name)

	if data.Value != nil {
		output += fmt.Sprintf(": %v", data.Value)
	}

	if data.Error != "" {
		output += fmt.Sprintf(" - ERROR: %s%s%s", "\033[31m", data.Error, resetColor)
	}

	if data.Duration != nil {
		output += fmt.Sprintf(" (%.2fms)", float64(data.Duration.Nanoseconds())/1e6)
	}

	if len(data.Labels) > 0 {
		output += fmt.Sprintf(" %v", data.Labels)
	}

	output += "\n"
	return output
}

// formatPlainOutput 格式化纯文本输出
func (cs *ConsoleSampler) formatPlainOutput(timestamp string, data *SampleData) string {
	output := fmt.Sprintf("[%s] %s [%s] %s",
		timestamp,
		data.Severity,
		data.MetricType,
		data.Name)

	if data.Value != nil {
		output += fmt.Sprintf(": %v", data.Value)
	}

	if data.Error != "" {
		output += fmt.Sprintf(" - ERROR: %s", data.Error)
	}

	if data.Duration != nil {
		output += fmt.Sprintf(" (%.2fms)", float64(data.Duration.Nanoseconds())/1e6)
	}

	if len(data.Labels) > 0 {
		output += fmt.Sprintf(" %v", data.Labels)
	}

	output += "\n"
	return output
}

// Flush 刷新（控制台无需刷新）
func (cs *ConsoleSampler) Flush() error {
	return nil
}

// Close 关闭控制台采样器
func (cs *ConsoleSampler) Close() error {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()
	cs.closed = true
	return nil
}

// GetStats 获取统计信息
func (cs *ConsoleSampler) GetStats() SamplerStats {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()
	return cs.stats
}

// BufferSampler 内存缓冲采样器
type BufferSampler struct {
	buffer   []*SampleData
	maxSize  int
	strategy SamplingStrategy
	stats    SamplerStats
	mutex    sync.RWMutex
	closed   bool
}

// NewBufferSampler 创建内存缓冲采样器
func NewBufferSampler(maxSize int) *BufferSampler {
	if maxSize <= 0 {
		maxSize = 1000 // 默认最大1000条
	}

	return &BufferSampler{
		buffer:   make([]*SampleData, 0, maxSize),
		maxSize:  maxSize,
		strategy: &AlwaysSample{},
	}
}

// SetStrategy 设置采样策略
func (bs *BufferSampler) SetStrategy(strategy SamplingStrategy) {
	bs.mutex.Lock()
	defer bs.mutex.Unlock()
	bs.strategy = strategy
}

// Sample 添加采样数据到缓冲区
func (bs *BufferSampler) Sample(data *SampleData) error {
	bs.mutex.Lock()
	defer bs.mutex.Unlock()

	if bs.closed {
		return fmt.Errorf("buffer sampler is closed")
	}

	// 检查是否应该采样
	if !bs.strategy.ShouldSample(data) {
		bs.stats.DroppedSamples++
		return nil
	}

	// 如果缓冲区满了，移除最老的数据
	if len(bs.buffer) >= bs.maxSize {
		bs.buffer = bs.buffer[1:]
	}

	// 创建数据副本，避免外部修改
	dataCopy := *data
	bs.buffer = append(bs.buffer, &dataCopy)

	bs.stats.TotalSamples++
	bs.stats.AcceptedSamples++
	bs.stats.LastSampleTime = time.Now()

	return nil
}

// GetBuffer 获取缓冲区数据（副本）
func (bs *BufferSampler) GetBuffer() []*SampleData {
	bs.mutex.RLock()
	defer bs.mutex.RUnlock()

	result := make([]*SampleData, len(bs.buffer))
	copy(result, bs.buffer)
	return result
}

// Clear 清空缓冲区
func (bs *BufferSampler) Clear() {
	bs.mutex.Lock()
	defer bs.mutex.Unlock()
	bs.buffer = bs.buffer[:0]
}

// Flush 刷新（内存缓冲无需刷新）
func (bs *BufferSampler) Flush() error {
	return nil
}

// Close 关闭缓冲采样器
func (bs *BufferSampler) Close() error {
	bs.mutex.Lock()
	defer bs.mutex.Unlock()
	bs.closed = true
	bs.buffer = nil
	return nil
}

// GetStats 获取统计信息
func (bs *BufferSampler) GetStats() SamplerStats {
	bs.mutex.RLock()
	defer bs.mutex.RUnlock()
	return bs.stats
}

// DumpToFile 将缓冲区数据导出到文件
func (bs *BufferSampler) DumpToFile(filepath string) error {
	bs.mutex.RLock()
	data := make([]*SampleData, len(bs.buffer))
	copy(data, bs.buffer)
	bs.mutex.RUnlock()

	file, err := os.OpenFile(filepath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
	if err != nil {
		return fmt.Errorf("failed to create dump file: %w", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")

	return encoder.Encode(data)
}
