#!/bin/bash

# 并发超时修复验证测试脚本
# 用于验证修复方案的有效性

echo "=========================================="
echo "🧪 并发超时修复验证测试"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试配置
TEST_DURATION=300  # 5分钟测试
MONITOR_INTERVAL=10  # 10秒监控间隔
LOG_DIR="./test_logs"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 创建日志目录
mkdir -p "$LOG_DIR"

# 测试结果文件
RESULT_FILE="$LOG_DIR/concurrent_test_result_$TIMESTAMP.txt"

# 初始化测试结果
init_test_results() {
    {
        echo "并发超时修复验证测试报告"
        echo "测试时间: $(date)"
        echo "测试持续时间: ${TEST_DURATION}秒"
        echo "=================================="
        echo ""
    } > "$RESULT_FILE"
}

# 记录测试结果
log_result() {
    local message="$1"
    echo -e "$message"
    echo "$message" >> "$RESULT_FILE"
}

# 检查前置条件
check_prerequisites() {
    log_result "📋 检查前置条件..."
    
    # 检查external_ids_sync是否存在
    if [ ! -f "./external_ids_sync" ]; then
        log_result "❌ external_ids_sync 可执行文件不存在"
        return 1
    fi
    
    # 检查Redis连接
    if ! redis-cli ping >/dev/null 2>&1; then
        log_result "⚠️  Redis连接失败，进程协调器可能无法工作"
    else
        log_result "✅ Redis连接正常"
    fi
    
    # 检查数据库连接
    log_result "✅ 前置条件检查完成"
    return 0
}

# 基线测试：单进程运行
baseline_test() {
    log_result ""
    log_result "🔍 基线测试：单进程运行"
    log_result "=================================="
    
    local start_time=$(date +%s)
    local baseline_log="$LOG_DIR/baseline_$TIMESTAMP.log"
    
    # 运行单进程测试
    timeout 60 ./external_ids_sync --cmd sync-batch --start-id 1 --end-id 50 --safe --workers 2 > "$baseline_log" 2>&1 &
    local baseline_pid=$!
    
    # 监控基线测试
    sleep 30
    if kill -0 $baseline_pid 2>/dev/null; then
        log_result "✅ 基线测试：单进程运行正常"
        kill $baseline_pid 2>/dev/null
    else
        log_result "❌ 基线测试：单进程运行异常"
        return 1
    fi
    
    # 分析基线日志
    if grep -q "timeout" "$baseline_log"; then
        log_result "⚠️  基线测试发现超时错误"
    else
        log_result "✅ 基线测试无超时错误"
    fi
    
    return 0
}

# 并发测试：两个进程同时运行
concurrent_test() {
    log_result ""
    log_result "🔥 并发测试：两个进程同时运行"
    log_result "=================================="
    
    local process1_log="$LOG_DIR/process1_$TIMESTAMP.log"
    local process2_log="$LOG_DIR/process2_$TIMESTAMP.log"
    local monitor_log="$LOG_DIR/monitor_$TIMESTAMP.log"
    
    # 启动监控
    start_monitoring "$monitor_log" &
    local monitor_pid=$!
    
    # 启动第一个进程
    log_result "🚀 启动进程1: ID范围 1-100"
    timeout $TEST_DURATION ./external_ids_sync --cmd sync-batch --start-id 1 --end-id 100 --safe --workers 2 > "$process1_log" 2>&1 &
    local process1_pid=$!
    
    # 等待5秒后启动第二个进程
    sleep 5
    log_result "🚀 启动进程2: ID范围 101-200"
    timeout $TEST_DURATION ./external_ids_sync --cmd sync-batch --start-id 101 --end-id 200 --safe --workers 2 > "$process2_log" 2>&1 &
    local process2_pid=$!
    
    # 监控并发运行
    local test_start=$(date +%s)
    local timeout_count=0
    local error_count=0
    
    while [ $(($(date +%s) - test_start)) -lt $TEST_DURATION ]; do
        sleep $MONITOR_INTERVAL
        
        # 检查进程状态
        local process1_running=false
        local process2_running=false
        
        if kill -0 $process1_pid 2>/dev/null; then
            process1_running=true
        fi
        
        if kill -0 $process2_pid 2>/dev/null; then
            process2_running=true
        fi
        
        log_result "📊 进程状态: P1=$process1_running, P2=$process2_running"
        
        # 检查超时错误
        local current_timeouts=$(grep -c "timeout" "$process1_log" "$process2_log" 2>/dev/null || echo 0)
        if [ "$current_timeouts" -gt "$timeout_count" ]; then
            log_result "⚠️  发现新的超时错误: $((current_timeouts - timeout_count))个"
            timeout_count=$current_timeouts
        fi
        
        # 如果两个进程都结束了，提前退出
        if [ "$process1_running" = false ] && [ "$process2_running" = false ]; then
            log_result "✅ 两个进程都已完成"
            break
        fi
    done
    
    # 清理进程
    kill $process1_pid $process2_pid $monitor_pid 2>/dev/null
    
    # 分析结果
    analyze_concurrent_results "$process1_log" "$process2_log" "$monitor_log"
}

# 启动系统监控
start_monitoring() {
    local monitor_log="$1"
    
    {
        echo "系统监控开始: $(date)"
        echo "=================================="
    } > "$monitor_log"
    
    while true; do
        {
            echo "时间: $(date)"
            echo "内存使用: $(free -h | grep Mem)"
            echo "网络连接数: $(netstat -an 2>/dev/null | grep ESTABLISHED | wc -l)"
            echo "API连接数: $(netstat -an 2>/dev/null | grep "118.196.31.23:5310" | wc -l)"
            echo "进程数: $(pgrep -f external_ids_sync | wc -l)"
            echo "文件描述符: $(lsof 2>/dev/null | wc -l)"
            echo "--------------------------------"
        } >> "$monitor_log"
        
        sleep $MONITOR_INTERVAL
    done
}

# 分析并发测试结果
analyze_concurrent_results() {
    local process1_log="$1"
    local process2_log="$2"
    local monitor_log="$3"
    
    log_result ""
    log_result "📊 并发测试结果分析"
    log_result "=================================="
    
    # 统计超时错误
    local timeout_count=$(grep -c "timeout" "$process1_log" "$process2_log" 2>/dev/null || echo 0)
    log_result "超时错误总数: $timeout_count"
    
    # 统计API调用失败
    local api_failures=$(grep -c "api_call_failed" "$process1_log" "$process2_log" 2>/dev/null || echo 0)
    log_result "API调用失败数: $api_failures"
    
    # 统计数据库查询失败
    local db_failures=$(grep -c "db_query_failed" "$process1_log" "$process2_log" 2>/dev/null || echo 0)
    log_result "数据库查询失败数: $db_failures"
    
    # 检查进程协调器工作情况
    local coordinator_logs=$(grep -c "进程协调器" "$process1_log" "$process2_log" 2>/dev/null || echo 0)
    if [ "$coordinator_logs" -gt 0 ]; then
        log_result "✅ 进程协调器正常工作"
    else
        log_result "⚠️  进程协调器可能未正常工作"
    fi
    
    # 检查资源监控器工作情况
    local monitor_logs=$(grep -c "资源监控器" "$process1_log" "$process2_log" 2>/dev/null || echo 0)
    if [ "$monitor_logs" -gt 0 ]; then
        log_result "✅ 资源监控器正常工作"
    else
        log_result "⚠️  资源监控器可能未正常工作"
    fi
    
    # 分析系统资源使用
    analyze_system_resources "$monitor_log"
    
    # 生成测试结论
    generate_test_conclusion "$timeout_count" "$api_failures" "$db_failures"
}

# 分析系统资源使用
analyze_system_resources() {
    local monitor_log="$1"
    
    log_result ""
    log_result "🔍 系统资源使用分析"
    log_result "=================================="
    
    # 分析网络连接数变化
    local max_connections=$(grep "网络连接数:" "$monitor_log" | awk '{print $2}' | sort -n | tail -1)
    local max_api_connections=$(grep "API连接数:" "$monitor_log" | awk '{print $2}' | sort -n | tail -1)
    
    log_result "最大网络连接数: $max_connections"
    log_result "最大API连接数: $max_api_connections"
    
    # 评估连接数是否合理
    if [ "$max_api_connections" -le 6 ]; then
        log_result "✅ API连接数控制良好 (≤6)"
    elif [ "$max_api_connections" -le 10 ]; then
        log_result "⚠️  API连接数偏高但可接受 (≤10)"
    else
        log_result "❌ API连接数过高 (>10)"
    fi
}

# 生成测试结论
generate_test_conclusion() {
    local timeout_count="$1"
    local api_failures="$2"
    local db_failures="$3"
    
    log_result ""
    log_result "🎯 测试结论"
    log_result "=================================="
    
    local score=100
    
    # 根据错误数量扣分
    if [ "$timeout_count" -gt 0 ]; then
        score=$((score - timeout_count * 10))
        log_result "❌ 仍有超时错误: -$((timeout_count * 10))分"
    fi
    
    if [ "$api_failures" -gt 0 ]; then
        score=$((score - api_failures * 5))
        log_result "❌ API调用失败: -$((api_failures * 5))分"
    fi
    
    if [ "$db_failures" -gt 0 ]; then
        score=$((score - db_failures * 5))
        log_result "❌ 数据库查询失败: -$((db_failures * 5))分"
    fi
    
    # 评估修复效果
    if [ "$score" -ge 90 ]; then
        log_result "🎉 修复效果优秀 (${score}/100)"
        log_result "✅ 并发超时问题已基本解决"
    elif [ "$score" -ge 70 ]; then
        log_result "👍 修复效果良好 (${score}/100)"
        log_result "✅ 并发超时问题有显著改善"
    elif [ "$score" -ge 50 ]; then
        log_result "⚠️  修复效果一般 (${score}/100)"
        log_result "⚠️  仍需进一步优化"
    else
        log_result "❌ 修复效果不佳 (${score}/100)"
        log_result "❌ 需要重新评估修复方案"
    fi
}

# 生成改进建议
generate_recommendations() {
    log_result ""
    log_result "💡 改进建议"
    log_result "=================================="
    
    log_result "1. 如果仍有超时问题："
    log_result "   - 进一步减少HTTP连接数"
    log_result "   - 增加进程间启动延迟"
    log_result "   - 检查API服务器连接限制"
    
    log_result "2. 如果资源使用过高："
    log_result "   - 减少worker数量"
    log_result "   - 增加请求间隔"
    log_result "   - 优化数据库连接池"
    
    log_result "3. 持续监控建议："
    log_result "   - 定期运行此测试脚本"
    log_result "   - 监控生产环境资源使用"
    log_result "   - 建立告警机制"
}

# 主测试流程
main() {
    init_test_results
    
    log_result "开始并发超时修复验证测试..."
    
    # 检查前置条件
    if ! check_prerequisites; then
        log_result "❌ 前置条件检查失败，测试终止"
        exit 1
    fi
    
    # 基线测试
    if ! baseline_test; then
        log_result "❌ 基线测试失败，跳过并发测试"
    else
        # 并发测试
        concurrent_test
    fi
    
    # 生成改进建议
    generate_recommendations
    
    log_result ""
    log_result "🎉 测试完成，详细结果请查看: $RESULT_FILE"
    log_result "📁 日志文件位置: $LOG_DIR"
}

# 运行测试
main "$@"
