# 🛡️ 防卡住功能使用指南

## ✅ 集成完成

防卡住功能已成功集成到您的 `main.go` 文件中！现在您的工具具备以下能力：

### 🔥 **新功能特性**

1. **自动超时控制** - 每个操作都有独立的超时限制
2. **分步骤执行** - 显示详细的执行步骤和进度
3. **智能重试** - API调用失败后自动短超时重试
4. **进度监控** - 实时显示处理进度和统计信息
5. **卡住检测** - 按 Ctrl+C 显示详细诊断信息
6. **内存监控** - 自动检测并处理内存使用过高情况

### 🚀 **立即使用**

#### 1. 测试单个剧集（推荐先测试）

```bash
# 使用新编译的防卡住版本
./external_ids_sync_anti_stuck --cmd sync-one --show-id 1 --config ../../config/local.ini --verbose

# 您将看到如下输出：
# 🛡️ 防卡住模式已启用，按 Ctrl+C 可查看诊断信息
# 🚀 [防卡住] 开始同步剧集 1...
# 🕐 开始执行 sync_show_1 (超时: 5m0s)...
# 📝 [步骤1/4] 检查现有记录...
# 📡 [步骤2/4] 调用外部API...
# ⚡ API调用完成，耗时: 2.34秒
# 📊 [步骤3/4] 获取同步结果...
# ✅ 剧集 1 同步完成！总耗时: 3.45秒
```

#### 2. 小批量测试

```bash
# 测试3个指定剧集
./external_ids_sync_anti_stuck --cmd sync-batch --show-ids "1,2,3" --config ../../config/local.ini --verbose

# 测试10个未同步的剧集
./external_ids_sync_anti_stuck --cmd sync-batch --all --limit-batch 10 --page-size 5 --config ../../config/local.ini --verbose
```

#### 3. 安全模式大批量处理

```bash
# 处理所有未同步剧集，使用安全参数
./external_ids_sync_anti_stuck --cmd sync-batch --all --page-size 20 --limit-batch 100 --config ../../config/local.ini --safe --verbose --yes
```

## 🛡️ **防卡住模式说明**

### 单个剧集同步 (sync-one)
- **总超时**: 5分钟
- **步骤1**: 检查现有记录 (30秒超时)
- **步骤2**: API调用 (60秒超时，失败后30秒重试)
- **步骤3**: 获取结果 (30秒超时)
- **详细日志**: 显示每个步骤的执行时间

### 批量同步 (sync-batch)
- **逐个处理**: 避免批量失败，每个剧集独立超时
- **进度报告**: 每10个剧集显示一次统计
- **内存监控**: 超过500MB自动执行GC
- **智能延迟**: 每个剧集间隔2秒，避免API限流

### 卡住检测
- **按 Ctrl+C**: 立即显示诊断信息
- **自动检测**: 超时后显示内存和Goroutine信息
- **堆栈跟踪**: 显示当前所有Goroutine的状态

## 🔍 **诊断和监控**

### 正常输出示例
```
🛡️ 防卡住模式已启用，按 Ctrl+C 可查看诊断信息
🚀 [防卡住批量] 开始同步 50 个剧集...
📍 [1/50] 正在处理剧集 1001...
🚀 [防卡住] 开始同步剧集 1001...
🕐 开始执行 sync_show_1001 (超时: 5m0s)...
📝 [步骤1/4] 检查现有记录...
✅ check_existing 执行成功
📡 [步骤2/4] 调用外部API...
✅ external_api_call 执行成功
⚡ API调用完成，耗时: 1.23秒
📊 [步骤3/4] 获取同步结果...
✅ get_result 执行成功
✅ 剧集 1001 同步完成！总耗时: 2.34秒
✅ 剧集 1001 处理成功
📊 当前统计: 成功 1, 失败 0, 进度 2.0%
```

### 超时输出示例
```
🕐 开始执行 external_api_call (超时: 1m0s)...
⏰ external_api_call 执行超时 (1m0s)
超时诊断信息:
  内存使用: 45.67 MB
  Goroutine数量: 12
⚠️  第一次尝试失败，使用短超时重试...
🕐 开始执行 external_api_call_retry (超时: 30s)...
✅ external_api_call_retry 执行成功
```

### 按 Ctrl+C 的诊断输出
```
^C
🚨 收到中断信号，正在诊断...
内存使用: 123.45 MB
Goroutine数量: 8
Goroutine堆栈:
goroutine 1 [running]:
main.AddStuckDetection.func1()
    /path/to/main.go:336 +0x123
...
```

## ⚡ **性能优化建议**

### 不同场景的推荐参数

#### 正常网络环境
```bash
./external_ids_sync_anti_stuck --cmd sync-batch --all --page-size 20 --limit-batch 200 --verbose
```

#### 网络不稳定
```bash
./external_ids_sync_anti_stuck --cmd sync-batch --all --page-size 10 --limit-batch 100 --safe --verbose
```

#### API频繁超时
```bash
./external_ids_sync_anti_stuck --cmd sync-batch --all --page-size 5 --limit-batch 50 --aggressive --verbose
```

#### 大批量快速处理
```bash
./external_ids_sync_anti_stuck --cmd sync-batch --all --page-size 30 --limit-batch 1000 --fast --yes
```

## 🎯 **解决您原来的卡住问题**

### 问题对比

**原来的问题**:
- ✅ 断路器状态正常（CLOSED），开始处理任务
- ❌ 然后5分钟无任何输出...

**现在的体验**:
- ✅ 🛡️ 防卡住模式已启用
- ✅ 🚀 [防卡住] 开始同步剧集...
- ✅ 📝 [步骤1/4] 检查现有记录...
- ✅ 📡 [步骤2/4] 调用外部API...
- ✅ ⚡ API调用完成，耗时: X秒
- ✅ 每个步骤都有实时反馈！

### 如果还是遇到问题

1. **按 Ctrl+C 查看诊断信息**
2. **检查超时日志，了解卡在哪一步**
3. **使用更小的 `--page-size` 参数**
4. **使用 `--aggressive` 模式快速跳过问题剧集**

## 📖 **下次使用**

现在您可以用新的可执行文件替换原来的：

```bash
# 备份原来的程序
mv external_ids_sync external_ids_sync_old

# 使用新的防卡住版本
mv external_ids_sync_anti_stuck external_ids_sync

# 或者重新编译原main.go（推荐）
go build -o external_ids_sync main.go
```

**恭喜！🎉 您的工具现在再也不会静默卡住了！**
